<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\JsonResponse;

class SettingController extends Controller
{
    /**
     * Get restaurant settings.
     */
    public function index(): JsonResponse
    {
        $settings = Setting::getSettings();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }
}
