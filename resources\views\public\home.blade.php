@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> Grills - Best Nyama Choma in Kiambu')
@section('description', 'Experience the finest nyama choma and grilled foods in Kiambu. Exceptional atmosphere, refreshing drinks, and memorable dining experiences.')

@section('content')

<!-- Sticky Location Button -->
<div class="fixed bottom-6 right-6 z-50">
	<button onclick="scrollToMap()" class="bg-amber-600 hover:bg-amber-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
		<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
			<path d="M12,2C8.13,2 5,5.13 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9C19,5.13 15.87,2 12,2M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z"/>
		</svg>
	</button>
</div>

<!-- Hero Section -->
<section class="relative h-screen flex items-center justify-center text-white overflow-hidden">
	<!-- Wooden Background -->
	<div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
		 style="background-image: url('{{ asset('images/wooden.jpeg') }}');">
		<div class="absolute inset-0 bg-black/40"></div>
	</div>

	<!-- Hero-contained Navbar -->
	<nav class="absolute top-0 left-0 right-0 z-50 bg-transparent">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between h-20">
				<div class="flex items-center">
					<a href="{{ route('home') }}" class="flex-shrink-0">
						<h1 class="text-2xl font-extrabold text-white tracking-wide">Kiambu Itaara Grills</h1>
					</a>
				</div>
				<!-- Desktop Navigation -->
				<div class="hidden md:flex items-center space-x-8">
					<a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">Home</a>
					<a href="{{ route('menu') }}" class="nav-link {{ request()->routeIs('menu') ? 'active' : '' }}">Menu</a>
					<a href="{{ route('gallery') }}" class="nav-link {{ request()->routeIs('gallery') ? 'active' : '' }}">Gallery</a>
					<a href="{{ route('events') }}" class="nav-link {{ request()->routeIs('events') ? 'active' : '' }}">Events</a>
					<a href="{{ route('contact') }}" class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}">Contact</a>
					@auth
						<a href="{{ route('admin.dashboard') }}" class="p-2 rounded-full bg-amber-600 hover:bg-amber-700 text-white transition-colors" title="Admin Dashboard">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/></svg>
						</a>
						<form method="POST" action="{{ route('logout') }}" class="inline">
							@csrf
							<button type="submit" class="p-2 rounded-full bg-red-600 hover:bg-red-700 text-white transition-colors ml-2" title="Logout">
								<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z"/></svg>
							</button>
						</form>
					@else
						<a href="{{ route('login') }}" class="p-2 rounded-full bg-amber-600 hover:bg-amber-700 text-white transition-colors" title="Login">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M10,17V14H3V10H10V7L15,12L10,17M10,2H19A2,2 0 0,1 21,4V20A2,2 0 0,1 19,22H10A2,2 0 0,1 8,20V18H10V20H19V4H10V6H8V4A2,2 0 0,1 10,2Z"/></svg>
						</a>
					@endauth
				</div>
				<!-- Mobile menu button -->
				<div class="md:hidden flex items-center">
					<button id="mobile-menu-button" class="text-white hover:text-amber-200 focus:outline-none p-2">
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
					</button>
				</div>
			</div>
		</div>
		<!-- Mobile Navigation -->
		<div id="mobile-menu" class="md:hidden bg-black/20 backdrop-blur-lg border-t border-white/10 hidden">
			<div class="px-2 pt-2 pb-3 space-y-1">
				<a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'active' : '' }}">Home</a>
				<a href="{{ route('menu') }}" class="mobile-nav-link {{ request()->routeIs('menu') ? 'active' : '' }}">Menu</a>
				<a href="{{ route('gallery') }}" class="mobile-nav-link {{ request()->routeIs('gallery') ? 'active' : '' }}">Gallery</a>
				<a href="{{ route('events') }}" class="mobile-nav-link {{ request()->routeIs('events') ? 'active' : '' }}">Events</a>
				<a href="{{ route('contact') }}" class="mobile-nav-link {{ request()->routeIs('contact') ? 'active' : '' }}">Contact</a>
				@auth
					<a href="{{ route('admin.dashboard') }}" class="mobile-nav-link bg-amber-600 text-white">
						<svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>
						Admin Dashboard
					</a>
					<form method="POST" action="{{ route('logout') }}" class="px-2">
						@csrf
						<button type="submit" class="mobile-nav-link bg-red-600 text-white w-full text-left">
							<svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 24 24"><path d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z"/></svg>
							Logout
						</button>
					</form>
				@else
					<a href="{{ route('login') }}" class="mobile-nav-link bg-amber-600 text-white">
						<svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 6h-3V5c0-.55-.45-1-1s-1 .45-1 1v3H8c-.55 0-1 .45-1 1s.45 1 1 1h3v3c0 .55.45 1 1 1s1-.45 1-1v-3h3c.55 0 1-.45 1-1s-.45-1-1-1z"/></svg>
						Admin Login
					</a>
				@endauth
			</div>
		</div>
	</nav>
	
	<div class="relative text-center px-4 z-10 fade-in">
		<h1 class="text-5xl md:text-7xl font-extrabold mb-6 text-white tracking-wide">
			{{ $homeContent->hero_title }}
		</h1>

		<p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-white/90 leading-relaxed font-semibold">
			{{ $homeContent->hero_subtitle }}
		</p>
		<div class="flex flex-col sm:flex-row gap-4 justify-center">
			<a href="{{ route('menu') }}" class="glass-effect text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white/20 transition-all duration-300 shadow-xl">View Menu</a>
			<a href="{{ route('contact') }}" class="glass-effect border-2 border-white/30 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white/20 hover:text-white transition-all duration-300">Contact Us</a>
		</div>
	</div>
</section>

<!-- About Section -->
<section class="py-20 bg-amber-50 relative smoke-pattern">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="text-center mb-16 fade-in">
			<h2 class="text-4xl md:text-5xl font-bold text-amber-900 mb-6">{{ $homeContent->about_title }}</h2>
			<p class="text-xl text-amber-800 max-w-4xl mx-auto leading-relaxed">{{ $homeContent->about_description }}</p>
		</div>
	</div>
</section>

<!-- Features Section -->
<section class="py-20 bg-white relative smoke-pattern">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="grid grid-cols-1 md:grid-cols-3 gap-12">
			<div class="text-center group fade-in">
				<div class="feature-icon bg-gradient-to-br from-amber-400 to-amber-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
					<svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12,2A2,2 0 0,1 14,4V8A2,2 0 0,1 12,10A2,2 0 0,1 10,8V4A2,2 0 0,1 12,2M21,9V7L15,1L9,7V9C9,10.1 9.9,11 11,11V16L7.5,17.5C7.09,17.66 6.84,18.08 6.84,18.5C6.84,19.17 7.38,19.72 8.05,19.72H15.95C16.62,19.72 17.16,19.17 17.16,18.5C17.16,18.08 16.91,17.66 16.5,17.5L13,16V11C14.1,11 15,10.1 15,9Z"/></svg>
				</div>
				<h3 class="text-2xl font-bold mb-4 text-amber-900">{{ $homeContent->feature_1_title }}</h3>
				<p class="text-amber-700 leading-relaxed">{{ $homeContent->feature_1_description }}</p>
			</div>
			<div class="text-center group fade-in">
				<div class="feature-icon bg-gradient-to-br from-amber-400 to-amber-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
					<svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M5,4V7H10.5V19H13.5V7H19V4H5M6,2H18V3H6V2Z"/></svg>
				</div>
				<h3 class="text-2xl font-bold mb-4 text-amber-900">{{ $homeContent->feature_2_title }}</h3>
				<p class="text-amber-700 leading-relaxed">{{ $homeContent->feature_2_description }}</p>
			</div>
			<div class="text-center group fade-in">
				<div class="feature-icon bg-gradient-to-br from-amber-400 to-amber-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
					<svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12C18,14.22 16.79,16.16 15,17.2V15A3,3 0 0,0 12,12A3,3 0 0,0 9,15V17.2C7.21,16.16 6,14.22 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12C8,13.11 8.45,14.1 9.2,14.83V15A1,1 0 0,1 10.2,16H13.8A1,1 0 0,1 14.8,15V14.83C15.55,14.1 16,13.11 16,12A4,4 0 0,0 12,8Z"/></svg>
				</div>
				<h3 class="text-2xl font-bold mb-4 text-amber-900">{{ $homeContent->feature_3_title }}</h3>
				<p class="text-amber-700 leading-relaxed">{{ $homeContent->feature_3_description }}</p>
			</div>
		</div>
	</div>
</section>

<!-- Upcoming Events Section -->
<section class="py-20 bg-white smoke-pattern">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="text-center mb-12 fade-in">
			<h2 class="text-4xl md:text-5xl font-bold text-amber-900 mb-6">Upcoming Events</h2>
			<p class="text-xl text-amber-700 max-w-2xl mx-auto">Don't miss out on our exciting events and live entertainment</p>
		</div>
		@if($upcomingEvents->count() > 0)
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 fade-in">
				@foreach($upcomingEvents as $event)
					<div class="glass-card rounded-lg overflow-hidden">
						@if($event->image_url)
							<img src="{{ asset('storage/' . $event->image_url) }}" alt="{{ $event->title }}" class="w-full h-48 object-cover">
						@else
							<div class="w-full h-48 bg-amber-100 flex items-center justify-center">
								<svg class="w-16 h-16 text-amber-600" fill="currentColor" viewBox="0 0 24 24"><path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/></svg>
							</div>
						@endif
						<div class="p-6">
							<div class="flex items-center mb-2">
								<svg class="w-4 h-4 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 24 24"><path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/></svg>
								<span class="text-sm text-amber-700">{{ \Carbon\Carbon::parse($event->event_date)->format('M d, Y - g:i A') }}</span>
							</div>
							<h3 class="text-lg font-semibold text-amber-900 mb-2">{{ $event->title }}</h3>
							@if($event->description)
								<p class="text-amber-700 text-sm">{{ Str::limit($event->description, 100) }}</p>
							@endif
						</div>
					</div>
				@endforeach
			</div>
		@else
			<div class="text-center py-12">
				<svg class="mx-auto h-16 w-16 text-amber-600 mb-4" fill="currentColor" viewBox="0 0 24 24"><path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/></svg>
				<h3 class="text-xl font-medium text-amber-900 mb-2">No upcoming events</h3>
				<p class="text-amber-700">Check back soon for exciting events and entertainment!</p>
			</div>
		@endif
	</div>
</section>

<!-- Contact & Location Section -->
<section class="py-20 bg-gradient-to-r from-amber-800 to-amber-900 text-white relative smoke-pattern">
	<div class="absolute inset-0 bg-gradient-to-br from-black/20 via-transparent to-black/30"></div>
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
		<div class="text-center mb-16 fade-in">
			<h2 class="text-4xl md:text-5xl font-bold mb-6 text-amber-100">Visit Us Today</h2>
			<p class="text-xl text-amber-200 max-w-2xl mx-auto">Experience the best nyama choma in Kiambu. We're open every day with great food and cold drinks waiting for you.</p>
		</div>
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 fade-in">
			<div>
				<h3 class="text-2xl font-bold mb-6 text-amber-100">Contact Information</h3>
				<div class="space-y-4">
					<div class="flex items-center">
						<a href="tel:{{ $settings->contact_phone ?? '+254XXXXXXXXX' }}" class="flex items-center text-amber-100 hover:text-white transition-colors group glass-card p-4 rounded-lg w-full">
							<div class="bg-amber-600 p-3 rounded-full mr-4 group-hover:bg-amber-500 transition-colors">
								<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/></svg>
							</div>
							<div>
								<p class="text-sm text-amber-200">Call Us</p>
								<p class="text-lg font-semibold">{{ $settings->contact_phone ?? '+254 XXX XXX XXX' }}</p>
							</div>
						</a>
					</div>
					<div class="flex items-center">
						<a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', $settings->contact_whatsapp ?? '+254XXXXXXXXX') }}" target="_blank" class="flex items-center text-amber-100 hover:text-white transition-colors group glass-card p-4 rounded-lg w-full">
							<div class="bg-green-600 p-3 rounded-full mr-4 group-hover:bg-green-500 transition-colors">
								<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M17.472,14.382c-0.297-0.149-1.758-0.867-2.03-0.967c-0.273-0.099-0.471-0.148-0.67,0.15c-0.197,0.297-0.767,0.966-0.94,1.164c-0.173,0.199-0.347,0.223-0.644,0.075c-0.297-0.15-1.255-0.463-2.39-1.475c-0.883-0.788-1.48-1.761-1.653-2.059c-0.173-0.297-0.018-0.458,0.13-0.606c0.134-0.133,0.298-0.347,0.446-0.52c0.149-0.174,0.198-0.298,0.298-0.497c0.099-0.198,0.05-0.371-0.025-0.52C9.611,9.2,9.208,7.85,8.961,7.13C8.717,6.437,8.469,6.616,8.287,6.604c-0.297-0.297-0.297-0.297-0.297-0.297s-0.198,0.025-0.297,0.025c-0.099,0-0.297,0.05-0.446,0.223C7.098,6.851,6.584,7.294,6.584,8.467C6.584,9.639,7.394,10.761,7.543,10.96c0.148,0.198,2.095,3.2,5.076,4.487c0.709,0.306,1.263,0.489,1.694,0.626c0.712,0.226,1.36,0.194,1.872,0.118c0.571-0.085,1.758-0.719,2.006-1.413c0.248-0.694,0.248-1.289,0.173-1.413C17.967,14.605,17.769,14.531,17.472,14.382z"/><path d="M12.057,2C6.501,2,2,6.501,2,12.057c0,1.784,0.465,3.463,1.281,4.91L2,22l5.184-1.281c1.408,0.777,3.01,1.224,4.873,1.224c5.556,0,10.057-4.501,10.057-10.057S17.613,2,12.057,2z M12.057,20.186c-1.54,0-2.983-0.426-4.226-1.166l-0.303-0.18l-3.145,0.778l0.794-3.081l-0.197-0.314c-0.814-1.293-1.281-2.809-1.281-4.423c0-4.452,3.624-8.076,8.076-8.076s8.076,3.624,8.076,8.076S16.509,20.186,12.057,20.186z"/></svg>
							</div>
							<div>
								<p class="text-sm text-amber-200">WhatsApp</p>
								<p class="text-lg font-semibold">{{ $settings->contact_whatsapp ?? '+254 XXX XXX XXX' }}</p>
							</div>
						</a>
					</div>
					@if($settings->contact_email)
						<div class="flex items-center">
							<a href="mailto:{{ $settings->contact_email }}" class="flex items-center text-amber-100 hover:text-white transition-colors group glass-card p-4 rounded-lg w-full">
								<div class="bg-amber-600 p-3 rounded-full mr-4 group-hover:bg-amber-500 transition-colors">
									<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/></svg>
								</div>
								<div>
									<p class="text-sm text-amber-200">Email</p>
									<p class="text-lg font-semibold">{{ $settings->contact_email }}</p>
								</div>
							</a>
						</div>
					@endif
				</div>
				<div class="mt-8 glass-card p-6 rounded-lg">
					<h4 class="text-xl font-bold mb-4 text-amber-100">Opening Hours</h4>
					<p class="text-amber-100 text-lg">{{ $settings->opening_hours ?? 'Monday - Sunday: 10:00 AM - 11:00 PM' }}</p>
				</div>
			</div>
			<div id="map-section">
				<h3 class="text-2xl font-bold mb-6 text-amber-100">Find Us</h3>
				<div class="rounded-lg overflow-hidden shadow-lg">
					<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.924646594641!2d36.83284287496555!3d-1.2127147987757112!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f3db33040b171%3A0xfe4bb7f148f6de98!2sItara%20Grill!5e0!3m2!1sen!2ske!4v1755690184168!5m2!1sen!2ske" width="100%" height="400" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
				</div>
				<div class="mt-4 text-center">
					<a href="https://maps.google.com/?q=Itara+Grill,+Kiambu" target="_blank" class="inline-flex items-center text-amber-200 hover:text-amber-100 transition-colors">
						<svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24"><path d="M12,2C8.13,2 5,5.13 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9C19,5.13 15.87,2 12,2M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z"/></svg>
						Open in Google Maps
					</a>
				</div>
			</div>
		</div>
	</div>
</section>

<script>
	function scrollToMap() {
		document.getElementById('map-section').scrollIntoView({ behavior: 'smooth', block: 'center' });
	}
</script>
@endsection
