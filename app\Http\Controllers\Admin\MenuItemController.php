<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Gallery;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class MenuItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $menuItems = MenuItem::with('category')->latest()->get();
        return view('admin.menu-items.index', compact('menuItems'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $categories = Category::all();
        $galleryImages = Gallery::with('category')->get();
        return view('admin.menu-items.create', compact('categories', 'galleryImages'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'gallery_image_id' => 'nullable|exists:galleries,id',
        ]);

        $data = $request->only(['name', 'description', 'price', 'category_id']);

        // Handle image - either upload new or use gallery image
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('menu-items', 'public');
            $data['image_url'] = $imagePath;
        } elseif ($request->gallery_image_id) {
            $galleryImage = Gallery::find($request->gallery_image_id);
            if ($galleryImage) {
                $data['image_url'] = $galleryImage->image_url;
            }
        }

        MenuItem::create($data);

        return redirect()->route('admin.menu-items.index')
            ->with('success', 'Menu item created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MenuItem $menuItem): View
    {
        $categories = Category::all();
        return view('admin.menu-items.edit', compact('menuItem', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MenuItem $menuItem): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        $data = $request->only(['name', 'description', 'price', 'category_id']);

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($menuItem->image_url) {
                \Storage::disk('public')->delete($menuItem->image_url);
            }

            $imagePath = $request->file('image')->store('menu-items', 'public');
            $data['image_url'] = $imagePath;
        }

        $menuItem->update($data);

        return redirect()->route('admin.menu-items.index')
            ->with('success', 'Menu item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MenuItem $menuItem)
    {
        // Delete image if exists
        if ($menuItem->image_url) {
            \Storage::disk('public')->delete($menuItem->image_url);
        }

        $menuItem->delete();

        // Return JSON for AJAX requests, redirect for regular requests
        if (request()->expectsJson()) {
            return response()->json(['success' => true, 'message' => 'Menu item deleted successfully']);
        }

        return redirect()->route('admin.menu-items.index')
            ->with('success', 'Menu item deleted successfully.');
    }

    /**
     * Update a single field via AJAX for inline editing.
     */
    public function updateField(Request $request, MenuItem $menuItem)
    {
        $request->validate([
            'field' => 'required|string',
            'value' => 'required|string',
        ]);

        $field = $request->field;
        $value = $request->value;

        // Validate field is allowed
        $allowedFields = ['name', 'description', 'price'];

        if (!in_array($field, $allowedFields)) {
            return response()->json(['success' => false, 'message' => 'Invalid field']);
        }

        $menuItem->update([$field => $value]);

        return response()->json(['success' => true, 'message' => 'Field updated successfully']);
    }

    /**
     * Update image via AJAX for inline editing.
     */
    public function updateImage(Request $request, MenuItem $menuItem)
    {
        $request->validate([
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'gallery_image_id' => 'nullable|exists:galleries,id',
        ]);

        $imageUrl = null;

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($menuItem->image_url) {
                \Storage::disk('public')->delete($menuItem->image_url);
            }

            $imagePath = $request->file('image')->store('menu-items', 'public');
            $imageUrl = $imagePath;
        } elseif ($request->gallery_image_id) {
            $galleryImage = Gallery::find($request->gallery_image_id);
            if ($galleryImage) {
                $imageUrl = $galleryImage->image_url;
            }
        }

        if ($imageUrl) {
            $menuItem->update(['image_url' => $imageUrl]);
            return response()->json([
                'success' => true,
                'message' => 'Image updated successfully',
                'image_url' => asset('storage/' . $imageUrl)
            ]);
        }

        return response()->json(['success' => false, 'message' => 'No image provided']);
    }
}
