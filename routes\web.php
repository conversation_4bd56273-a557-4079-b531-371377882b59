<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PublicController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\MenuItemController;
use App\Http\Controllers\Admin\GalleryController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\SettingController;
use Illuminate\Support\Facades\Route;

// Public routes - Blade templates
Route::get('/', [PublicController::class, 'home'])->name('home');
Route::get('/menu', [PublicController::class, 'menu'])->name('menu');
Route::get('/gallery', [PublicController::class, 'gallery'])->name('gallery');
Route::get('/events', [PublicController::class, 'events'])->name('events');
Route::get('/contact', [PublicController::class, 'contact'])->name('contact');

// Dashboard redirect route (required by <PERSON><PERSON>ze)
Route::get('/dashboard', function () {
    return redirect()->route('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Test route to verify login
Route::get('/test-login', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    return response()->json([
        'user_exists' => $user ? true : false,
        'user_data' => $user ? $user->only(['id', 'name', 'email']) : null,
        'password_check' => $user ? \Illuminate\Support\Facades\Hash::check('password123', $user->password) : false,
        'auth_check' => auth()->check(),
        'current_user' => auth()->user() ? auth()->user()->only(['id', 'name', 'email']) : null,
    ]);
});

// Test upload functionality
Route::get('/test-upload', function () {
    return view('test-upload');
});

Route::post('/test-upload', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'test_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
    ]);

    if ($request->hasFile('test_image')) {
        $path = $request->file('test_image')->store('test', 'public');
        return response()->json([
            'success' => true,
            'path' => $path,
            'url' => asset('storage/' . $path),
            'file_info' => [
                'original_name' => $request->file('test_image')->getClientOriginalName(),
                'size' => $request->file('test_image')->getSize(),
                'mime_type' => $request->file('test_image')->getMimeType(),
            ]
        ]);
    }

    return response()->json(['success' => false, 'message' => 'No file uploaded']);
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Resource routes
    Route::resource('categories', CategoryController::class);
    Route::resource('menu-items', MenuItemController::class);
    Route::resource('gallery', GalleryController::class);
    Route::resource('events', EventController::class);

    // Settings routes (not a full resource since we typically have only one settings record)
    Route::get('settings', [SettingController::class, 'edit'])->name('settings.edit');
    Route::put('settings', [SettingController::class, 'update'])->name('settings.update');

    // Inline editing routes
    Route::post('settings/update-field', [SettingController::class, 'updateField']);
    Route::post('menu-items/{menuItem}/update-field', [MenuItemController::class, 'updateField']);
    Route::post('menu-items/{menuItem}/update-image', [MenuItemController::class, 'updateImage']);
    Route::post('events/{event}/update-field', [EventController::class, 'updateField']);
    Route::post('events/{event}/update-image', [EventController::class, 'updateImage']);
    Route::get('gallery/options', [GalleryController::class, 'getOptions']);

    // Home page editing
    Route::get('home/edit', [DashboardController::class, 'editHomePage'])->name('home.edit');
    Route::post('home/update', [DashboardController::class, 'updateHomePage'])->name('home.update');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
