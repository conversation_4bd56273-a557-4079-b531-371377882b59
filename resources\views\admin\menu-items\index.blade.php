@extends('layouts.app')

@section('title', 'Manage Menu Items - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-amber-900">Manage Menu Items</h1>
                <p class="text-amber-700 mt-2">Add, edit, and organize your restaurant's menu</p>
            </div>
            <a href="{{ route('admin.menu-items.create') }}" class="bg-amber-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors">
                Add New Menu Item
            </a>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <!-- Menu Items Grid -->
        @if($menuItems->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($menuItems as $item)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <!-- Image -->
                        @if($item->image_url)
                            <img src="{{ asset('storage/' . $item->image_url) }}" alt="{{ $item->name }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-amber-100 flex items-center justify-center">
                                <svg class="w-16 h-16 text-amber-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.1,13.34L3.91,9.16C2.35,7.59 2.35,5.06 3.91,3.5L10.93,10.5L8.1,13.34M14.88,11.53C16.28,12.92 16.28,15.18 14.88,16.57C13.49,17.96 11.23,17.96 9.84,16.57L7.02,13.75L9.84,10.93L14.88,11.53Z"/>
                                </svg>
                            </div>
                        @endif

                        <!-- Content -->
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="text-lg font-semibold text-amber-900">{{ $item->name }}</h3>
                                <span class="text-xl font-bold text-amber-600">KSh {{ number_format($item->price, 0) }}</span>
                            </div>
                            
                            <p class="text-sm text-amber-600 mb-2">{{ $item->category->name }}</p>
                            
                            @if($item->description)
                                <p class="text-amber-700 text-sm mb-4">{{ Str::limit($item->description, 100) }}</p>
                            @endif

                            <!-- Actions -->
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-amber-600">{{ $item->created_at->format('M d, Y') }}</span>
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.menu-items.edit', $item) }}" class="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors">
                                        Edit
                                    </a>
                                    <form method="POST" action="{{ route('admin.menu-items.destroy', $item) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this menu item?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition-colors">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div class="bg-white rounded-lg shadow-lg p-12 text-center">
                <svg class="mx-auto h-16 w-16 text-amber-600 mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.1,13.34L3.91,9.16C2.35,7.59 2.35,5.06 3.91,3.5L10.93,10.5L8.1,13.34M14.88,11.53C16.28,12.92 16.28,15.18 14.88,16.57C13.49,17.96 11.23,17.96 9.84,16.57L7.02,13.75L9.84,10.93L14.88,11.53Z"/>
                </svg>
                <h3 class="text-xl font-medium text-amber-900 mb-2">No menu items yet</h3>
                <p class="text-amber-700 mb-6">Start building your menu by adding your first item</p>
                <a href="{{ route('admin.menu-items.create') }}" class="bg-amber-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors">
                    Add First Menu Item
                </a>
            </div>
        @endif

        <!-- Back to Dashboard -->
        <div class="mt-8">
            <a href="{{ route('admin.dashboard') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                ← Back to Dashboard
            </a>
        </div>
    </div>
</div>
@endsection
