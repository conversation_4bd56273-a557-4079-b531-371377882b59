<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test File Upload</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">Test File Upload</h1>
        
        <form id="uploadForm" enctype="multipart/form-data">
            @csrf
            <div class="mb-4">
                <label for="test_image" class="block text-sm font-medium text-gray-700 mb-2">
                    Select Image
                </label>
                <input type="file" 
                       id="test_image" 
                       name="test_image" 
                       accept="image/*"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       required>
            </div>
            
            <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                Upload Test Image
            </button>
        </form>
        
        <div id="result" class="mt-6 hidden">
            <h3 class="text-lg font-semibold mb-2">Upload Result:</h3>
            <pre id="resultContent" class="bg-gray-100 p-3 rounded text-sm overflow-auto"></pre>
        </div>
        
        <div id="preview" class="mt-6 hidden">
            <h3 class="text-lg font-semibold mb-2">Uploaded Image:</h3>
            <img id="previewImage" class="w-full rounded-lg" alt="Uploaded image">
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('test_image');
            const csrfToken = document.querySelector('input[name="_token"]').value;
            
            formData.append('test_image', fileInput.files[0]);
            formData.append('_token', csrfToken);
            
            try {
                const response = await fetch('/test-upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                document.getElementById('resultContent').textContent = JSON.stringify(result, null, 2);
                document.getElementById('result').classList.remove('hidden');
                
                if (result.success && result.url) {
                    document.getElementById('previewImage').src = result.url;
                    document.getElementById('preview').classList.remove('hidden');
                }
                
            } catch (error) {
                document.getElementById('resultContent').textContent = 'Error: ' + error.message;
                document.getElementById('result').classList.remove('hidden');
            }
        });
    </script>
</body>
</html>
