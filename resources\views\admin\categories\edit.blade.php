@extends('layouts.app')

@section('title', 'Edit Category - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Edit Category</h1>
            <p class="text-amber-700 mt-2">Update the category information</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form method="POST" action="{{ route('admin.categories.update', $category) }}">
                @csrf
                @method('PUT')

                <!-- Category Name -->
                <div class="mb-6">
                    <label for="name" class="block text-sm font-medium text-amber-900 mb-2">
                        Category Name *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $category->name) }}"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('name') border-red-500 @enderror"
                           placeholder="e.g., Grills & Nyama Choma, Drinks & Beverages"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category Stats -->
                <div class="mb-6 p-4 bg-amber-50 rounded-lg">
                    <h3 class="text-sm font-medium text-amber-900 mb-2">Category Usage:</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-amber-700">Menu Items:</span>
                            <span class="font-semibold text-amber-900">{{ $category->menuItems()->count() }}</span>
                        </div>
                        <div>
                            <span class="text-amber-700">Gallery Items:</span>
                            <span class="font-semibold text-amber-900">{{ $category->galleryItems()->count() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('admin.categories.index') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        ← Back to Categories
                    </a>
                    <div class="space-x-3">
                        <button type="button" onclick="window.history.back()" class="px-4 py-2 border border-amber-300 text-amber-700 rounded-md hover:bg-amber-50 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-amber-700 transition-colors">
                            Update Category
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Danger Zone -->
        @if($category->menuItems()->count() == 0 && $category->galleryItems()->count() == 0)
            <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 class="text-sm font-medium text-red-900 mb-2">Delete Category</h3>
                <p class="text-sm text-red-700 mb-3">This category is not being used and can be safely deleted.</p>
                <form method="POST" action="{{ route('admin.categories.destroy', $category) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors">
                        Delete Category
                    </button>
                </form>
            </div>
        @else
            <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 class="text-sm font-medium text-yellow-900 mb-2">Cannot Delete</h3>
                <p class="text-sm text-yellow-700">This category cannot be deleted because it contains menu items or gallery photos. Please move or delete the items first.</p>
            </div>
        @endif
    </div>
</div>
@endsection
