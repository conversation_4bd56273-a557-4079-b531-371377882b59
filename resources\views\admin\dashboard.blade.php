@extends('layouts.app')

@section('title', 'Admin Dashboard - <PERSON><PERSON><PERSON>aar<PERSON> Grills')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Admin Dashboard</h1>
            <p class="text-amber-700 mt-2">Welcome to the Kiambu Itaara Grills management system</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center">
                    <div class="bg-amber-600 p-3 rounded-full">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-amber-900">Categories</h3>
                        <p class="text-2xl font-bold text-amber-600">{{ $stats['categories'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center">
                    <div class="bg-amber-600 p-3 rounded-full">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8.1,13.34L3.91,9.16C2.35,7.59 2.35,5.06 3.91,3.5L10.93,10.5L8.1,13.34M14.88,11.53C16.28,12.92 16.28,15.18 14.88,16.57C13.49,17.96 11.23,17.96 9.84,16.57L7.02,13.75L9.84,10.93L14.88,11.53Z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-amber-900">Menu Items</h3>
                        <p class="text-2xl font-bold text-amber-600">{{ $stats['menu_items'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center">
                    <div class="bg-amber-600 p-3 rounded-full">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9,2V7.38L10.5,8.88L15.12,4.26L19.74,8.88L12,16.62L4.26,8.88L8.88,4.26L9,4.15V2M12,5L7,10L12,15L17,10L12,5Z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-amber-900">Gallery Items</h3>
                        <p class="text-2xl font-bold text-amber-600">{{ $stats['gallery_items'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center">
                    <div class="bg-amber-600 p-3 rounded-full">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-amber-900">Events</h3>
                        <p class="text-2xl font-bold text-amber-600">{{ $stats['events'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-amber-900 mb-4">Website Content</h3>
                <p class="text-amber-700 mb-4">Edit your home page content and settings</p>
                <div class="space-y-2">
                    <a href="{{ route('admin.home.edit') }}" class="block bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700 transition-colors text-center">
                        Edit Home Page
                    </a>
                    <a href="{{ route('admin.settings.edit') }}" class="block bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700 transition-colors text-center">
                        Contact Settings
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-amber-900 mb-4">Manage Menu</h3>
                <p class="text-amber-700 mb-4">Add, edit, or remove menu items and categories</p>
                <div class="space-y-2">
                    <a href="{{ route('admin.categories.index') }}" class="block bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700 transition-colors text-center">
                        Manage Categories
                    </a>
                    <a href="{{ route('admin.menu-items.index') }}" class="block bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700 transition-colors text-center">
                        Manage Menu Items
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-amber-900 mb-4">Gallery & Events</h3>
                <p class="text-amber-700 mb-4">Upload photos and manage events</p>
                <div class="space-y-2">
                    <a href="{{ route('admin.gallery.index') }}" class="block bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700 transition-colors text-center">
                        Manage Gallery
                    </a>
                    <a href="{{ route('admin.events.index') }}" class="block bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700 transition-colors text-center">
                        Manage Events
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold text-amber-900 mb-4">Settings</h3>
                <p class="text-amber-700 mb-4">Update restaurant information and contact details</p>
                <div class="space-y-2">
                    <a href="{{ route('admin.settings.edit') }}" class="block bg-amber-600 text-white px-4 py-2 rounded hover:bg-amber-700 transition-colors text-center">
                        Restaurant Settings
                    </a>
                    <a href="{{ route('home') }}" target="_blank" class="block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors text-center">
                        View Public Site
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-amber-900 mb-4">Quick Info</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-amber-800 mb-2">Upcoming Events</h4>
                    <p class="text-amber-700">{{ $stats['upcoming_events'] }} upcoming events scheduled</p>
                </div>
                <div>
                    <h4 class="font-semibold text-amber-800 mb-2">System Status</h4>
                    <p class="text-green-600 font-medium">All systems operational</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
