@extends('layouts.app')

@section('title', 'Events & Specials - <PERSON><PERSON><PERSON> Itaara Grills')
@section('description', 'Stay updated with our upcoming events, Premier League screenings, and special offers at Kiambu Itaara Grills.')

@section('content')
<div class="min-h-screen bg-amber-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 fade-in">
            <h1 class="text-4xl md:text-5xl font-bold text-amber-900 mb-4">Events & Specials</h1>
            <p class="text-xl text-amber-700 max-w-2xl mx-auto">
                Stay updated with our upcoming events, Premier League screenings, and special offers
            </p>
        </div>

        @if($upcomingEvents->count() > 0)
            <div class="mb-16 fade-in">
                <h2 class="text-3xl font-bold text-amber-900 mb-8 text-center">Upcoming Events</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($upcomingEvents as $event)
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            @if($event->image_url)
                                <img src="{{ Storage::url($event->image_url) }}" alt="{{ $event->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-amber-100 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-amber-600" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                                    </svg>
                                </div>
                            @endif
                            
                            <div class="p-6">
                                <div class="flex items-center mb-3">
                                    <svg class="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                                    </svg>
                                    <span class="text-amber-700 font-medium">{{ $event->event_date->format('M d, Y - g:i A') }}</span>
                                </div>
                                
                                <h3 class="text-xl font-bold text-amber-900 mb-3">{{ $event->title }}</h3>
                                <p class="text-amber-700 leading-relaxed">{{ $event->description }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if($allEvents->count() > 0 && $allEvents->count() > $upcomingEvents->count())
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-amber-900 mb-8 text-center">Recent Events</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($allEvents->skip($upcomingEvents->count()) as $event)
                        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                                </svg>
                                <span class="text-amber-700 font-medium">{{ $event->event_date->format('M d, Y') }}</span>
                            </div>
                            
                            <h3 class="text-lg font-bold text-amber-900 mb-2">{{ $event->title }}</h3>
                            <p class="text-amber-700">{{ Str::limit($event->description, 120) }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if($upcomingEvents->count() == 0 && $allEvents->count() == 0)
            <div class="text-center py-20">
                <div class="bg-white rounded-lg shadow-lg p-12 max-w-2xl mx-auto fade-in">
                    <svg class="w-16 h-16 text-amber-600 mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                    </svg>
                    <h3 class="text-2xl font-bold text-amber-900 mb-4">Events Coming Soon</h3>
                    <p class="text-amber-700 text-lg font-medium">We're planning exciting events and specials</p>
                    <p class="text-amber-600 mt-2">Follow us on social media to stay updated on upcoming events</p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
