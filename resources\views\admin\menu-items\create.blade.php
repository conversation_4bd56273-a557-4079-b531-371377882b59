@extends('layouts.app')

@section('title', 'Add Menu Item - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Add New Menu Item</h1>
            <p class="text-amber-700 mt-2">Add a delicious item to your restaurant menu</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form method="POST" action="{{ route('admin.menu-items.store') }}" enctype="multipart/form-data">
                @csrf

                <!-- Item Name -->
                <div class="mb-6">
                    <label for="name" class="block text-sm font-medium text-amber-900 mb-2">
                        Item Name *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name') }}"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('name') border-red-500 @enderror"
                           placeholder="e.g., Beef Nyama Choma, Tusker Beer"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div class="mb-6">
                    <label for="category_id" class="block text-sm font-medium text-amber-900 mb-2">
                        Category *
                    </label>
                    <select id="category_id" 
                            name="category_id" 
                            class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('category_id') border-red-500 @enderror"
                            required>
                        <option value="">Select a category</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @if($categories->count() == 0)
                        <p class="mt-1 text-sm text-amber-600">
                            <a href="{{ route('admin.categories.create') }}" class="text-amber-700 hover:text-amber-900 underline">Create a category first</a>
                        </p>
                    @endif
                </div>

                <!-- Price -->
                <div class="mb-6">
                    <label for="price" class="block text-sm font-medium text-amber-900 mb-2">
                        Price (KSh) *
                    </label>
                    <input type="number" 
                           id="price" 
                           name="price" 
                           value="{{ old('price') }}"
                           step="0.01"
                           min="0"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('price') border-red-500 @enderror"
                           placeholder="e.g., 1200.00"
                           required>
                    @error('price')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-amber-900 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3"
                              class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('description') border-red-500 @enderror"
                              placeholder="Describe the dish, ingredients, or preparation method...">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Photo Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-amber-900 mb-2">
                        Item Photo
                    </label>

                    <!-- Photo Selection Tabs -->
                    <div class="mb-4">
                        <div class="flex space-x-4 border-b border-amber-200">
                            <button type="button" onclick="showPhotoTab('upload')" id="upload-tab" class="px-4 py-2 text-sm font-medium text-amber-700 border-b-2 border-amber-600">
                                Upload New
                            </button>
                            <button type="button" onclick="showPhotoTab('gallery')" id="gallery-tab" class="px-4 py-2 text-sm font-medium text-amber-600 border-b-2 border-transparent hover:border-amber-300">
                                Choose from Gallery
                            </button>
                        </div>
                    </div>

                    <!-- Upload New Photo -->
                    <div id="upload-section" class="photo-section">
                        <input type="file"
                               id="image"
                               name="image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('image') border-red-500 @enderror">
                        <p class="mt-1 text-sm text-amber-600">Upload a high-quality photo of the dish (JPG, PNG, max 2MB)</p>
                    </div>

                    <!-- Choose from Gallery -->
                    <div id="gallery-section" class="photo-section hidden">
                        <select id="gallery_image_id"
                                name="gallery_image_id"
                                class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500">
                            <option value="">Select a photo from gallery</option>
                            @foreach($galleryImages as $galleryImage)
                                <option value="{{ $galleryImage->id }}" data-image="{{ asset('storage/' . $galleryImage->image_url) }}">
                                    {{ $galleryImage->title }} ({{ $galleryImage->category->name }})
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-sm text-amber-600">Choose an existing photo from your gallery</p>

                        <!-- Preview Selected Gallery Image -->
                        <div id="gallery-preview" class="mt-3 hidden">
                            <img id="gallery-preview-img" src="" alt="Preview" class="w-32 h-32 object-cover rounded-lg">
                        </div>

                        @if($galleryImages->count() == 0)
                            <p class="mt-2 text-sm text-amber-600">
                                <a href="{{ route('admin.gallery.create') }}" class="text-amber-700 hover:text-amber-900 underline">Upload photos to gallery first</a>
                            </p>
                        @endif
                    </div>

                    @error('image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('admin.menu-items.index') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        ← Back to Menu Items
                    </a>
                    <div class="space-x-3">
                        <button type="button" onclick="window.history.back()" class="px-4 py-2 border border-amber-300 text-amber-700 rounded-md hover:bg-amber-50 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-amber-700 transition-colors">
                            Add Menu Item
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showPhotoTab(tab) {
    // Hide all sections
    document.querySelectorAll('.photo-section').forEach(section => {
        section.classList.add('hidden');
    });

    // Remove active class from all tabs
    document.querySelectorAll('[id$="-tab"]').forEach(tabBtn => {
        tabBtn.classList.remove('text-amber-700', 'border-amber-600');
        tabBtn.classList.add('text-amber-600', 'border-transparent');
    });

    // Show selected section and activate tab
    if (tab === 'upload') {
        document.getElementById('upload-section').classList.remove('hidden');
        document.getElementById('upload-tab').classList.add('text-amber-700', 'border-amber-600');
        document.getElementById('upload-tab').classList.remove('text-amber-600', 'border-transparent');

        // Clear gallery selection
        document.getElementById('gallery_image_id').value = '';
        document.getElementById('gallery-preview').classList.add('hidden');
    } else {
        document.getElementById('gallery-section').classList.remove('hidden');
        document.getElementById('gallery-tab').classList.add('text-amber-700', 'border-amber-600');
        document.getElementById('gallery-tab').classList.remove('text-amber-600', 'border-transparent');

        // Clear file input
        document.getElementById('image').value = '';
    }
}

// Gallery image preview
document.getElementById('gallery_image_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const imageUrl = selectedOption.getAttribute('data-image');

    if (imageUrl) {
        document.getElementById('gallery-preview-img').src = imageUrl;
        document.getElementById('gallery-preview').classList.remove('hidden');
    } else {
        document.getElementById('gallery-preview').classList.add('hidden');
    }
});
</script>
@endsection
