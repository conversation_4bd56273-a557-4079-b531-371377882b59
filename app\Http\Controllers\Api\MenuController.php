<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\MenuItem;
use Illuminate\Http\JsonResponse;

class MenuController extends Controller
{
    /**
     * Get all menu items grouped by category.
     */
    public function index(): JsonResponse
    {
        $categories = Category::with(['menuItems' => function ($query) {
            $query->orderBy('name');
        }])->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get menu items by category.
     */
    public function byCategory($categoryId): JsonResponse
    {
        $category = Category::with(['menuItems' => function ($query) {
            $query->orderBy('name');
        }])->findOrFail($categoryId);

        return response()->json([
            'success' => true,
            'data' => $category
        ]);
    }
}
