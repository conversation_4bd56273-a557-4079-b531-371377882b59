<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Gallery extends Model
{
    protected $table = 'gallery';

    protected $fillable = [
        'title',
        'description',
        'category_id',
        'image_url',
    ];

    /**
     * Get the category that owns the gallery item.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
