@extends('layouts.app')

@section('title', 'Add Event - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Add New Event</h1>
            <p class="text-amber-700 mt-2">Create an exciting event for your customers</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form method="POST" action="{{ route('admin.events.store') }}" enctype="multipart/form-data">
                @csrf

                <!-- Event Title -->
                <div class="mb-6">
                    <label for="title" class="block text-sm font-medium text-amber-900 mb-2">
                        Event Title *
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title') }}"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('title') border-red-500 @enderror"
                           placeholder="e.g., EPL Match: Arsenal vs Chelsea, Live Music Night"
                           required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Event Date -->
                <div class="mb-6">
                    <label for="event_date" class="block text-sm font-medium text-amber-900 mb-2">
                        Event Date & Time *
                    </label>
                    <input type="datetime-local" 
                           id="event_date" 
                           name="event_date" 
                           value="{{ old('event_date') }}"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('event_date') border-red-500 @enderror"
                           required>
                    @error('event_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-amber-900 mb-2">
                        Event Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('description') border-red-500 @enderror"
                              placeholder="Describe the event, special offers, what to expect...">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Image Upload -->
                <div class="mb-6">
                    <label for="image" class="block text-sm font-medium text-amber-900 mb-2">
                        Event Photo
                    </label>
                    <input type="file" 
                           id="image" 
                           name="image" 
                           accept="image/*"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('image') border-red-500 @enderror">
                    @error('image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-amber-600">Upload a promotional image for the event (JPG, PNG, max 2MB)</p>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('admin.events.index') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        ← Back to Events
                    </a>
                    <div class="space-x-3">
                        <button type="button" onclick="window.history.back()" class="px-4 py-2 border border-amber-300 text-amber-700 rounded-md hover:bg-amber-50 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-amber-700 transition-colors">
                            Create Event
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Event Ideas -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-900 mb-2">Event Ideas:</h3>
            <ul class="text-sm text-blue-800 space-y-1">
                <li>• <strong>Sports:</strong> EPL matches, Champions League, World Cup games</li>
                <li>• <strong>Entertainment:</strong> Live music, DJ nights, karaoke</li>
                <li>• <strong>Specials:</strong> Happy hour, weekend promotions, holiday celebrations</li>
                <li>• <strong>Community:</strong> Local team support, cultural events</li>
            </ul>
        </div>
    </div>
</div>
@endsection
