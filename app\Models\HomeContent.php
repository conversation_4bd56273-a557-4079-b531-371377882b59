<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class HomeContent extends Model
{
    protected $fillable = [
        'hero_title',
        'hero_subtitle',
        'about_title',
        'about_description',
        'feature_1_title',
        'feature_1_description',
        'feature_2_title',
        'feature_2_description',
        'feature_3_title',
        'feature_3_description',
    ];

    /**
     * Get the home content instance (singleton pattern)
     */
    public static function getContent()
    {
        return static::first() ?: static::create([
            'hero_title' => 'Kiambu Itaara Grills',
            'hero_subtitle' => 'Experience the finest nyama choma and grilled foods in Kiambu. Exceptional atmosphere, refreshing drinks, and unforgettable dining experiences.',
            'about_title' => 'Welcome to Kiambu Itaara Grills',
            'about_description' => 'Located in the heart of Kiambu, we serve the finest grilled foods with a focus on authentic nyama choma, creating a vibrant and welcoming atmosphere perfect for friends, families, and sports enthusiasts. Our commitment to quality and tradition makes every visit memorable.',
            'feature_1_title' => 'Premium Nyama Choma',
            'feature_1_description' => 'Perfectly grilled meat with authentic Kenyan spices and traditional cooking methods that deliver exceptional flavor in every bite.',
            'feature_2_title' => 'Refreshing Beverages',
            'feature_2_description' => 'Enjoy our extensive selection of cold beers, crafted cocktails, and refreshing non-alcoholic beverages to complement your meal perfectly.',
            'feature_3_title' => 'Live Sports & Events',
            'feature_3_description' => 'Watch Premier League matches and enjoy live entertainment in our vibrant atmosphere designed for socializing and memorable experiences.',
        ]);
    }
}
