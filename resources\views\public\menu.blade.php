@extends('layouts.app')

@section('title', 'Our Menu - Kiambu <PERSON>aara Grills')
@section('description', 'Discover our delicious selection of authentic grilled foods and refreshing beverages at Kiambu Itaara Grills.')

@section('content')
<div class="min-h-screen bg-amber-50 py-12 relative">
    @auth
        <div class="absolute top-4 right-4 z-10">
            <a href="{{ route('admin.menu-items.index') }}"
               class="inline-flex items-center px-3 py-2 bg-amber-600/20 backdrop-blur-sm text-amber-800 rounded-lg hover:bg-amber-600/30 transition-colors text-sm">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
                </svg>
                <PERSON><PERSON>u
            </a>
        </div>
    @endauth

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 fade-in">
            <h1 class="text-4xl md:text-5xl font-bold text-amber-900 mb-4">Our Menu</h1>
            <p class="text-xl text-amber-700 max-w-2xl mx-auto">
                Discover our delicious selection of authentic grilled foods and refreshing beverages
            </p>
        </div>

        @if($categories->count() > 0)
            @foreach($categories as $category)
                <div class="mb-16 fade-in">
                    <h2 class="text-3xl font-bold text-amber-900 mb-8 text-center">{{ $category->name }}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($category->menuItems as $item)
                            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 relative">
                                @auth
                                    <!-- Delete Button (Admin Only) -->
                                    <button onclick="deleteMenuItem({{ $item->id }})"
                                            class="absolute top-2 left-2 bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity z-10 hover:bg-red-700">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                                        </svg>
                                    </button>
                                @endauth

                                <!-- Editable Image -->
                                <x-inline-edit-image
                                    :src="$item->image_url"
                                    :alt="$item->name"
                                    field="image_url"
                                    model="menu_item"
                                    :id="$item->id"
                                    class="w-full h-48 object-cover" />

                                <div class="p-6">
                                    <div class="flex justify-between items-start mb-2">
                                        <!-- Editable Name -->
                                        <x-inline-edit-text
                                            :value="$item->name"
                                            field="name"
                                            model="menu_item"
                                            :id="$item->id"
                                            class="text-xl font-semibold text-amber-900" />

                                        <!-- Editable Price -->
                                        <div class="text-2xl font-bold text-amber-600">
                                            KSh <x-inline-edit-text
                                                :value="$item->price"
                                                field="price"
                                                model="menu_item"
                                                :id="$item->id"
                                                type="number"
                                                class="inline" />
                                        </div>
                                    </div>

                                    <!-- Editable Description -->
                                    <x-inline-edit-text
                                        :value="$item->description"
                                        field="description"
                                        model="menu_item"
                                        :id="$item->id"
                                        type="textarea"
                                        placeholder="Add description..."
                                        class="text-amber-700 leading-relaxed" />
                                </div>
                            </div>
                        @endforeach

                        @auth
                            <!-- Add New Menu Item Card (Admin Only) -->
                            <div class="bg-amber-50 border-2 border-dashed border-amber-300 rounded-lg flex items-center justify-center min-h-[300px] hover:border-amber-400 transition-colors">
                                <a href="{{ route('admin.menu-items.create') }}" class="text-center p-6">
                                    <svg class="w-16 h-16 text-amber-600 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                                    </svg>
                                    <h3 class="text-lg font-semibold text-amber-900 mb-2">Add New Item</h3>
                                    <p class="text-amber-700">Add a new menu item to this category</p>
                                </a>
                            </div>
                        @endauth
                    </div>
                </div>
            @endforeach
        @else
            <div class="text-center py-20">
                <div class="bg-white rounded-lg shadow-lg p-12 max-w-2xl mx-auto fade-in">
                    <svg class="w-16 h-16 text-amber-600 mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8.1,13.34L3.91,9.16C2.35,7.59 2.35,5.06 3.91,3.5L10.93,10.5L8.1,13.34M14.88,11.53C16.28,12.92 16.28,15.18 14.88,16.57C13.49,17.96 11.23,17.96 9.84,16.57L7.02,13.75L9.84,10.93L14.88,11.53Z"/>
                    </svg>
                    <h3 class="text-2xl font-bold text-amber-900 mb-4">Menu Coming Soon</h3>
                    <p class="text-amber-700 text-lg font-medium">Our delicious menu is being prepared</p>
                    <p class="text-amber-600 mt-2">Please check back soon or contact us for current offerings</p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
