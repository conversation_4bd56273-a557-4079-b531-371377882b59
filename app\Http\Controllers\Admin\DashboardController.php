<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\MenuItem;
use App\Models\Gallery;
use App\Models\Event;
use App\Models\HomeContent;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index(): View
    {
        $stats = [
            'categories' => Category::count(),
            'menu_items' => MenuItem::count(),
            'gallery_items' => Gallery::count(),
            'events' => Event::count(),
            'upcoming_events' => Event::upcoming()->count(),
        ];

        return view('admin.dashboard', compact('stats'));
    }

    /**
     * Show the home page editing interface.
     */
    public function editHomePage(): View
    {
        $homeContent = HomeContent::getContent();
        return view('admin.home.edit', compact('homeContent'));
    }

    /**
     * Update the home page content.
     */
    public function updateHomePage(Request $request)
    {
        $request->validate([
            'hero_title' => 'required|string|max:255',
            'hero_subtitle' => 'required|string',
            'about_title' => 'required|string|max:255',
            'about_description' => 'required|string',
            'feature_1_title' => 'required|string|max:255',
            'feature_1_description' => 'required|string',
            'feature_2_title' => 'required|string|max:255',
            'feature_2_description' => 'required|string',
            'feature_3_title' => 'required|string|max:255',
            'feature_3_description' => 'required|string',
        ]);

        $homeContent = HomeContent::getContent();
        $homeContent->update($request->all());

        return redirect()->route('admin.home.edit')
            ->with('success', 'Home page content updated successfully!');
    }
}
