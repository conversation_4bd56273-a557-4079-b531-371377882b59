<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $fillable = [
        'opening_hours',
        'contact_phone',
        'contact_whatsapp',
        'contact_email',
        'facebook_url',
        'instagram_url',
        'tiktok_url',
    ];

    /**
     * Get the first (and typically only) settings record.
     */
    public static function getSettings()
    {
        return static::first() ?? new static();
    }
}
