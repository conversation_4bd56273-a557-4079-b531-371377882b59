<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class SettingController extends Controller
{
    /**
     * Show the form for editing the settings.
     */
    public function edit(): View
    {
        $settings = Setting::getSettings();
        return view('admin.settings.edit', compact('settings'));
    }

    /**
     * Update the settings in storage.
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'opening_hours' => 'nullable|string',
            'contact_phone' => 'nullable|string|max:20',
            'contact_whatsapp' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'facebook_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'tiktok_url' => 'nullable|url|max:255',
        ]);

        $settings = Setting::first();

        if ($settings) {
            $settings->update($request->all());
        } else {
            Setting::create($request->all());
        }

        return redirect()->route('admin.settings.edit')
            ->with('success', 'Settings updated successfully.');
    }

    /**
     * Update a single field via AJAX for inline editing.
     */
    public function updateField(Request $request)
    {
        $request->validate([
            'field' => 'required|string',
            'value' => 'nullable|string',
        ]);

        $field = $request->field;
        $value = $request->value;

        // Validate field is allowed
        $allowedFields = [
            'hero_title', 'hero_subtitle', 'about_title', 'about_description',
            'feature_1_title', 'feature_1_description',
            'feature_2_title', 'feature_2_description',
            'feature_3_title', 'feature_3_description',
            'opening_hours', 'contact_phone', 'contact_whatsapp', 'contact_email'
        ];

        if (!in_array($field, $allowedFields)) {
            return response()->json(['success' => false, 'message' => 'Invalid field']);
        }

        $settings = Setting::first();
        if ($settings) {
            $settings->update([$field => $value]);
        } else {
            Setting::create([$field => $value]);
        }

        return response()->json(['success' => true, 'message' => 'Field updated successfully']);
    }
}
