<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    protected $fillable = [
        'title',
        'description',
        'event_date',
        'image_url',
    ];

    protected $casts = [
        'event_date' => 'datetime',
    ];

    /**
     * Scope a query to only include upcoming events.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('event_date', '>=', now());
    }

    /**
     * Scope a query to order events by date.
     */
    public function scopeOrderByDate($query)
    {
        return $query->orderBy('event_date', 'asc');
    }
}
