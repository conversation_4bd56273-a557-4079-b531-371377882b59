@extends('layouts.app')

@section('title', 'Add Category - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Add New Category</h1>
            <p class="text-amber-700 mt-2">Create a new category for organizing menu items and gallery photos</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form method="POST" action="{{ route('admin.categories.store') }}">
                @csrf

                <!-- Category Name -->
                <div class="mb-6">
                    <label for="name" class="block text-sm font-medium text-amber-900 mb-2">
                        Category Name *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name') }}"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('name') border-red-500 @enderror"
                           placeholder="e.g., Grills & Nyama Choma, Drinks & Beverages"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-amber-600">This will be used to organize your menu items and gallery photos</p>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('admin.categories.index') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        ← Back to Categories
                    </a>
                    <div class="space-x-3">
                        <button type="button" onclick="window.history.back()" class="px-4 py-2 border border-amber-300 text-amber-700 rounded-md hover:bg-amber-50 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-amber-700 transition-colors">
                            Create Category
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Help Text -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-900 mb-2">Category Examples:</h3>
            <ul class="text-sm text-blue-800 space-y-1">
                <li>• <strong>Menu:</strong> Grills & Nyama Choma, Drinks & Beverages, Appetizers, Cocktails</li>
                <li>• <strong>Gallery:</strong> Food Photos, Restaurant Atmosphere, Events & Celebrations</li>
            </ul>
        </div>
    </div>
</div>
@endsection
