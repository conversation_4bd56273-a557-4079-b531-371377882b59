<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\MenuItem;
use App\Models\Gallery;
use App\Models\Event;
use App\Models\HomeContent;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PublicController extends Controller
{
    /**
     * Display the homepage.
     */
    public function home(): View
    {
        $settings = Setting::getSettings();
        $homeContent = HomeContent::getContent();
        $upcomingEvents = Event::where('event_date', '>=', now())
                              ->orderBy('event_date', 'asc')
                              ->limit(3)
                              ->get();

        return view('public.home', compact('settings', 'homeContent', 'upcomingEvents'));
    }

    /**
     * Display the menu page.
     */
    public function menu(): View
    {
        $categories = Category::with(['menuItems' => function ($query) {
            $query->orderBy('name');
        }])->whereHas('menuItems')->orderBy('name')->get();

        return view('public.menu', compact('categories'));
    }

    /**
     * Display the gallery page.
     */
    public function gallery(): View
    {
        $categories = Category::with(['galleryItems' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }])->whereHas('galleryItems')->orderBy('name')->get();

        return view('public.gallery', compact('categories'));
    }

    /**
     * Display the events page.
     */
    public function events(): View
    {
        $upcomingEvents = Event::upcoming()->orderByDate()->get();
        $allEvents = Event::orderBy('event_date', 'desc')->limit(10)->get();

        return view('public.events', compact('upcomingEvents', 'allEvents'));
    }

    /**
     * Display the contact page.
     */
    public function contact(): View
    {
        $settings = Setting::getSettings();
        return view('public.contact', compact('settings'));
    }
}
