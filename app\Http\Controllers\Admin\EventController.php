<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $filter = $request->get('filter', 'all');
        $now = now();

        $query = Event::query();

        switch ($filter) {
            case 'upcoming':
                $query->where('event_date', '>=', $now);
                $events = $query->orderBy('event_date', 'asc')->get();
                break;
            case 'past':
                $query->where('event_date', '<', $now);
                $events = $query->orderBy('event_date', 'desc')->get();
                break;
            default:
                $events = $query->orderBy('event_date', 'desc')->get();
        }

        $upcomingCount = Event::where('event_date', '>=', $now)->count();
        $pastCount = Event::where('event_date', '<', $now)->count();
        $totalCount = Event::count();

        return view('admin.events.index', compact('events', 'filter', 'upcomingCount', 'pastCount', 'totalCount'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('admin.events.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'event_date' => 'required|date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        $data = $request->only(['title', 'description', 'event_date']);

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('events', 'public');
            $data['image_url'] = $imagePath;
        }

        Event::create($data);

        return redirect()->route('admin.events.index')
            ->with('success', 'Event created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Event $event): View
    {
        return view('admin.events.edit', compact('event'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Event $event): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'event_date' => 'required|date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        $data = $request->only(['title', 'description', 'event_date']);

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($event->image_url) {
                \Storage::disk('public')->delete($event->image_url);
            }

            $imagePath = $request->file('image')->store('events', 'public');
            $data['image_url'] = $imagePath;
        }

        $event->update($data);

        return redirect()->route('admin.events.index')
            ->with('success', 'Event updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Event $event): RedirectResponse
    {
        // Delete image if exists
        if ($event->image_url) {
            \Storage::disk('public')->delete($event->image_url);
        }

        $event->delete();

        return redirect()->route('admin.events.index')
            ->with('success', 'Event deleted successfully.');
    }
}
