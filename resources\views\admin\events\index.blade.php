@extends('layouts.app')

@section('title', 'Manage Events - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-amber-900">Manage Events</h1>
                <p class="text-amber-700 mt-2">Create and manage events, specials, and EPL screenings</p>
            </div>
            <a href="{{ route('admin.events.create') }}" class="bg-amber-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors">
                Add New Event
            </a>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <!-- Event Filters -->
        <div class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('admin.events.index', ['filter' => 'all']) }}"
                   class="px-4 py-2 rounded-lg text-sm font-medium transition-colors {{ $filter === 'all' ? 'bg-amber-600 text-white' : 'bg-white text-amber-700 border border-amber-300 hover:bg-amber-50' }}">
                    All Events ({{ $totalCount }})
                </a>
                <a href="{{ route('admin.events.index', ['filter' => 'upcoming']) }}"
                   class="px-4 py-2 rounded-lg text-sm font-medium transition-colors {{ $filter === 'upcoming' ? 'bg-green-600 text-white' : 'bg-white text-green-700 border border-green-300 hover:bg-green-50' }}">
                    Upcoming ({{ $upcomingCount }})
                </a>
                <a href="{{ route('admin.events.index', ['filter' => 'past']) }}"
                   class="px-4 py-2 rounded-lg text-sm font-medium transition-colors {{ $filter === 'past' ? 'bg-gray-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50' }}">
                    Past Events ({{ $pastCount }})
                </a>
            </div>
        </div>

        <!-- Events List -->
        @if($events->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($events as $event)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <!-- Image -->
                        @if($event->image_url)
                            <img src="{{ asset('storage/' . $event->image_url) }}" alt="{{ $event->title }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-amber-100 flex items-center justify-center">
                                <svg class="w-16 h-16 text-amber-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                                </svg>
                            </div>
                        @endif

                        <!-- Content -->
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                                    </svg>
                                    <span class="text-sm text-amber-700">{{ \Carbon\Carbon::parse($event->event_date)->format('M d, Y - g:i A') }}</span>
                                </div>

                                <!-- Event Status Badge -->
                                @if(\Carbon\Carbon::parse($event->event_date)->isFuture())
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Upcoming
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Past
                                    </span>
                                @endif
                            </div>

                            <h3 class="text-lg font-semibold text-amber-900 mb-2">{{ $event->title }}</h3>

                            @if($event->description)
                                <p class="text-amber-700 text-sm mb-4">{{ Str::limit($event->description, 100) }}</p>
                            @endif

                            <!-- Actions -->
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-amber-600">{{ $event->created_at->format('Created M d') }}</span>
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.events.edit', $event) }}" class="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors">
                                        Edit
                                    </a>
                                    <form method="POST" action="{{ route('admin.events.destroy', $event) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this event?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition-colors">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div class="bg-white rounded-lg shadow-lg p-12 text-center">
                <svg class="mx-auto h-16 w-16 text-amber-600 mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
                </svg>
                <h3 class="text-xl font-medium text-amber-900 mb-2">No events yet</h3>
                <p class="text-amber-700 mb-6">Start promoting your restaurant by creating your first event</p>
                <a href="{{ route('admin.events.create') }}" class="bg-amber-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors">
                    Create First Event
                </a>
            </div>
        @endif

        <!-- Back to Dashboard -->
        <div class="mt-8">
            <a href="{{ route('admin.dashboard') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                ← Back to Dashboard
            </a>
        </div>
    </div>
</div>
@endsection
