<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class GalleryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $galleryItems = Gallery::with('category')->latest()->get();
        return view('admin.gallery.index', compact('galleryItems'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $categories = Category::all();
        return view('admin.gallery.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        $data = $request->only(['title', 'description', 'category_id']);

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('gallery', 'public');
            $data['image_url'] = $imagePath;
        }

        Gallery::create($data);

        return redirect()->route('admin.gallery.index')
            ->with('success', 'Photo uploaded successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Gallery $gallery): View
    {
        $categories = Category::all();
        return view('admin.gallery.edit', compact('gallery', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Gallery $gallery): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        $data = $request->only(['title', 'description', 'category_id']);

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($gallery->image_url) {
                \Storage::disk('public')->delete($gallery->image_url);
            }

            $imagePath = $request->file('image')->store('gallery', 'public');
            $data['image_url'] = $imagePath;
        }

        $gallery->update($data);

        return redirect()->route('admin.gallery.index')
            ->with('success', 'Photo updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Gallery $gallery): RedirectResponse
    {
        // Delete image if exists
        if ($gallery->image_url) {
            \Storage::disk('public')->delete($gallery->image_url);
        }

        $gallery->delete();

        return redirect()->route('admin.gallery.index')
            ->with('success', 'Photo deleted successfully.');
    }

    /**
     * Get gallery options for dropdowns.
     */
    public function getOptions()
    {
        $galleryItems = Gallery::with('category')->get()->map(function ($item) {
            return [
                'id' => $item->id,
                'title' => $item->title,
                'category' => $item->category->name,
            ];
        });

        return response()->json($galleryItems);
    }
}
