<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Gallery;
use Illuminate\Http\JsonResponse;

class GalleryController extends Controller
{
    /**
     * Get all gallery items grouped by category.
     */
    public function index(): JsonResponse
    {
        $categories = Category::with(['galleryItems' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }])->whereHas('galleryItems')->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get gallery items by category.
     */
    public function byCategory($categoryId): JsonResponse
    {
        $category = Category::with(['galleryItems' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }])->findOrFail($categoryId);

        return response()->json([
            'success' => true,
            'data' => $category
        ]);
    }
}
