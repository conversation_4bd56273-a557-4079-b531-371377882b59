@extends('layouts.app')

@section('title', 'Restaurant Settings - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Contact Settings</h1>
            <p class="text-amber-700 mt-2">Update your restaurant's contact information and social media links</p>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form method="POST" action="{{ route('admin.settings.update') }}">
                @csrf
                @method('PUT')

                <!-- Contact Information Section -->
                <div class="mb-8 pb-6 border-b border-amber-200">
                    <h3 class="text-lg font-medium text-amber-900 mb-4">Contact Information</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Opening Hours -->
                    <div class="md:col-span-2">
                        <label for="opening_hours" class="block text-sm font-medium text-amber-900 mb-2">
                            Opening Hours
                        </label>
                        <textarea id="opening_hours"
                                  name="opening_hours"
                                  rows="2"
                                  class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('opening_hours') border-red-500 @enderror"
                                  placeholder="Monday - Sunday: 10:00 AM - 11:00 PM">{{ old('opening_hours', $settings->opening_hours) }}</textarea>
                        @error('opening_hours')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-amber-600">Leave empty to use default hours</p>
                    </div>

                    <!-- Contact Phone -->
                    <div>
                        <label for="contact_phone" class="block text-sm font-medium text-amber-900 mb-2">
                            Contact Phone
                        </label>
                        <input type="text"
                               id="contact_phone"
                               name="contact_phone"
                               value="{{ old('contact_phone', $settings->contact_phone) }}"
                               class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('contact_phone') border-red-500 @enderror"
                               placeholder="+254 XXX XXX XXX">
                        @error('contact_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-amber-600">Leave empty to use default phone</p>
                    </div>

                    <!-- WhatsApp -->
                    <div>
                        <label for="contact_whatsapp" class="block text-sm font-medium text-amber-900 mb-2">
                            WhatsApp Number
                        </label>
                        <input type="text" 
                               id="contact_whatsapp" 
                               name="contact_whatsapp" 
                               value="{{ old('contact_whatsapp', $settings->contact_whatsapp) }}"
                               class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('contact_whatsapp') border-red-500 @enderror"
                               placeholder="+254 XXX XXX XXX">
                        @error('contact_whatsapp')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div class="md:col-span-2">
                        <label for="contact_email" class="block text-sm font-medium text-amber-900 mb-2">
                            Contact Email
                        </label>
                        <input type="email" 
                               id="contact_email" 
                               name="contact_email" 
                               value="{{ old('contact_email', $settings->contact_email) }}"
                               class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('contact_email') border-red-500 @enderror"
                               placeholder="<EMAIL>">
                        @error('contact_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Social Media Section -->
                <div class="mt-8 pt-6 border-t border-amber-200">
                    <h3 class="text-lg font-medium text-amber-900 mb-4">Social Media Links</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Facebook -->
                        <div>
                            <label for="facebook_url" class="block text-sm font-medium text-amber-900 mb-2">
                                Facebook Page URL
                            </label>
                            <input type="url" 
                                   id="facebook_url" 
                                   name="facebook_url" 
                                   value="{{ old('facebook_url', $settings->facebook_url) }}"
                                   class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('facebook_url') border-red-500 @enderror"
                                   placeholder="https://facebook.com/kiambugrills">
                            @error('facebook_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Instagram -->
                        <div>
                            <label for="instagram_url" class="block text-sm font-medium text-amber-900 mb-2">
                                Instagram Profile URL
                            </label>
                            <input type="url" 
                                   id="instagram_url" 
                                   name="instagram_url" 
                                   value="{{ old('instagram_url', $settings->instagram_url) }}"
                                   class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('instagram_url') border-red-500 @enderror"
                                   placeholder="https://instagram.com/kiambugrills">
                            @error('instagram_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- TikTok -->
                        <div class="md:col-span-2">
                            <label for="tiktok_url" class="block text-sm font-medium text-amber-900 mb-2">
                                TikTok Profile URL
                            </label>
                            <input type="url" 
                                   id="tiktok_url" 
                                   name="tiktok_url" 
                                   value="{{ old('tiktok_url', $settings->tiktok_url) }}"
                                   class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('tiktok_url') border-red-500 @enderror"
                                   placeholder="https://tiktok.com/@kiambugrills">
                            @error('tiktok_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-between items-center">
                    <a href="{{ route('admin.dashboard') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        ← Back to Dashboard
                    </a>
                    <div class="space-x-3">
                        <a href="{{ route('home') }}" target="_blank" class="px-4 py-2 border border-amber-300 text-amber-700 rounded-md hover:bg-amber-50 transition-colors">
                            Preview Site
                        </a>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-amber-700 transition-colors">
                            Save Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
