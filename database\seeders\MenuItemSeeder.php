<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\MenuItem;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MenuItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get category IDs
        $grillsCategory = Category::where('name', 'Grills & Nyama Choma')->first();
        $drinksCategory = Category::where('name', 'Drinks & Beverages')->first();
        $cocktailsCategory = Category::where('name', 'Cocktails')->first();
        $appetizersCategory = Category::where('name', 'Appetizers')->first();

        $menuItems = [
            // Grills & Nyama Choma
            [
                'name' => 'Beef Nyama Choma',
                'description' => 'Perfectly grilled beef with traditional Kenyan spices',
                'price' => 1200.00,
                'category_id' => $grillsCategory->id,
            ],
            [
                'name' => 'Goat Meat (<PERSON>buzi)',
                'description' => 'Tender grilled goat meat, a local favorite',
                'price' => 1500.00,
                'category_id' => $grillsCategory->id,
            ],
            [
                'name' => 'Chicken Grills',
                'description' => 'Juicy grilled chicken with herbs and spices',
                'price' => 800.00,
                'category_id' => $grillsCategory->id,
            ],
            [
                'name' => 'Fish Grills',
                'description' => 'Fresh grilled fish with lemon and herbs',
                'price' => 900.00,
                'category_id' => $grillsCategory->id,
            ],

            // Drinks & Beverages
            [
                'name' => 'Tusker Beer',
                'description' => 'Kenya\'s premium lager beer',
                'price' => 250.00,
                'category_id' => $drinksCategory->id,
            ],
            [
                'name' => 'White Cap Beer',
                'description' => 'Refreshing local beer',
                'price' => 230.00,
                'category_id' => $drinksCategory->id,
            ],
            [
                'name' => 'Coca Cola',
                'description' => 'Classic soft drink',
                'price' => 120.00,
                'category_id' => $drinksCategory->id,
            ],
            [
                'name' => 'Fresh Juice',
                'description' => 'Freshly squeezed fruit juice',
                'price' => 200.00,
                'category_id' => $drinksCategory->id,
            ],

            // Cocktails
            [
                'name' => 'Dawa Cocktail',
                'description' => 'Traditional Kenyan honey-based cocktail',
                'price' => 400.00,
                'category_id' => $cocktailsCategory->id,
            ],
            [
                'name' => 'Mojito',
                'description' => 'Classic mint and lime cocktail',
                'price' => 450.00,
                'category_id' => $cocktailsCategory->id,
            ],

            // Appetizers
            [
                'name' => 'Samosas',
                'description' => 'Crispy pastries filled with meat or vegetables',
                'price' => 150.00,
                'category_id' => $appetizersCategory->id,
            ],
            [
                'name' => 'Chicken Wings',
                'description' => 'Spicy grilled chicken wings',
                'price' => 300.00,
                'category_id' => $appetizersCategory->id,
            ],
        ];

        foreach ($menuItems as $item) {
            MenuItem::create($item);
        }
    }
}
