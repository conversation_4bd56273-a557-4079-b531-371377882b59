<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'Grills & Nyama Choma'],
            ['name' => 'Drinks & Beverages'],
            ['name' => 'Cocktails'],
            ['name' => 'Appetizers'],
            ['name' => 'Gallery - Food'],
            ['name' => 'Gallery - Atmosphere'],
            ['name' => 'Gallery - Events'],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
