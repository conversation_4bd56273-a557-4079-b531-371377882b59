<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', config('app.name', 'Kiambu Itaara Grills'))</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="@yield('description', 'Experience the best nyama choma and grilled foods in Kiambu. Great ambience, cold drinks, and unforgettable moments at Kiambu Itaara Grills.')">
    <meta name="keywords" content="@yield('keywords', 'nyama choma, grilled food, bar, restaurant, Kiambu, Kenya, drinks, ambience')">
    <meta name="author" content="Kiambu Itaara Grills">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', 'Kiambu Itaara Grills - Best Nyama Choma in Kiambu')">
    <meta property="og:description" content="@yield('og_description', 'Experience the best nyama choma and grilled foods in Kiambu. Great ambience, cold drinks, and unforgettable moments.')">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:image" content="{{ asset('images/og-image.jpg') }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Inline Editing Script -->
    @auth
        <script src="{{ asset('js/inline-edit.js') }}"></script>
        <meta name="csrf-token" content="{{ csrf_token() }}">
    @endauth

    <!-- Custom Styles -->
    <style>
        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #92400e, #b45309, #d97706);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }
        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading-text {
            color: white;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }

        /* Fade In Animations */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }
        .animate-fade-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Smoke and Diagonal Effects */
        .smoke-pattern {
            position: relative;
            overflow: hidden;
        }
        .smoke-pattern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(45deg, rgba(180, 83, 9, 0.02) 1px, transparent 1px),
                linear-gradient(-45deg, rgba(180, 83, 9, 0.02) 1px, transparent 1px),
                linear-gradient(135deg, rgba(180, 83, 9, 0.01) 1px, transparent 1px),
                radial-gradient(ellipse at center, rgba(180, 83, 9, 0.01) 0%, transparent 70%),
                radial-gradient(ellipse at 20% 80%, rgba(180, 83, 9, 0.015) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 20%, rgba(180, 83, 9, 0.015) 0%, transparent 50%);
            background-size: 30px 30px, 30px 30px, 60px 60px, 200px 100px, 150px 80px, 150px 80px;
            background-position: 0 0, 15px 15px, 0 0, 0 0, 0 0, 0 0;
            pointer-events: none;
            z-index: 1;
        }
        .smoke-pattern > * {
            position: relative;
            z-index: 2;
        }

        /* Glass Effects */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        /* Feature Icon Hover Effects */
        .feature-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .feature-icon:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 20px 40px rgba(180, 83, 9, 0.3);
        }
        .feature-icon svg {
            transition: transform 0.3s ease;
        }
        .feature-icon:hover svg {
            transform: scale(1.1);
        }
        .nav-link {
            @apply px-4 py-2 rounded-md text-sm font-extrabold transition-all duration-300 text-white hover:text-white hover:bg-white/10 hover:backdrop-blur-sm hover:shadow-lg;
        }
        .nav-link.active {
            @apply text-white bg-white/20 backdrop-blur-sm shadow-lg;
        }
        .mobile-nav-link {
            @apply block px-3 py-2 rounded-md text-base font-extrabold transition-colors text-white hover:text-white hover:bg-white/10;
        }
        .mobile-nav-link.active {
            @apply text-white bg-white/20;
        }
    </style>
</head>
<body class="font-sans antialiased bg-amber-50">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <div class="loading-text">
            <div>Loading Kiambu Itaara Grills...</div>
            <div style="font-size: 14px; margin-top: 8px; opacity: 0.8;">Preparing your dining experience</div>
        </div>
    </div>
    <!-- Navigation -->
    @php $isHome = request()->routeIs('home'); @endphp
    @unless($isHome)
    <nav class="bg-black/20 backdrop-blur-lg shadow-2xl sticky top-0 z-50 border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="flex-shrink-0">
                        <h1 class="text-2xl font-extrabold text-white tracking-wide">
                            Kiambu Itaara Grills
                        </h1>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                        Home
                    </a>
                    <a href="{{ route('menu') }}" class="nav-link {{ request()->routeIs('menu') ? 'active' : '' }}">
                        Menu
                    </a>
                    <a href="{{ route('gallery') }}" class="nav-link {{ request()->routeIs('gallery') ? 'active' : '' }}">
                        Gallery
                    </a>
                    <a href="{{ route('events') }}" class="nav-link {{ request()->routeIs('events') ? 'active' : '' }}">
                        Events
                    </a>
                    <a href="{{ route('contact') }}" class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}">
                        Contact
                    </a>

                    <!-- Admin Login -->
                    @auth
                        <a href="{{ route('admin.dashboard') }}" class="p-2 rounded-full bg-amber-600 hover:bg-amber-700 text-white transition-colors" title="Admin Dashboard">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                            </svg>
                        </a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="p-2 rounded-full bg-red-600 hover:bg-red-700 text-white transition-colors ml-2" title="Logout">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z"/>
                                </svg>
                            </button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="p-2 rounded-full bg-amber-600 hover:bg-amber-700 text-white transition-colors" title="Login">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M10,17V14H3V10H10V7L15,12L10,17M10,2H19A2,2 0 0,1 21,4V20A2,2 0 0,1 19,22H10A2,2 0 0,1 8,20V18H10V20H19V4H10V6H8V4A2,2 0 0,1 10,2Z"/>
                            </svg>
                        </a>
                    @endauth
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-amber-100 hover:text-amber-200 focus:outline-none p-2">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden bg-black/20 backdrop-blur-lg border-t border-white/10 hidden">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                    Home
                </a>
                <a href="{{ route('menu') }}" class="mobile-nav-link {{ request()->routeIs('menu') ? 'active' : '' }}">
                    Menu
                </a>
                <a href="{{ route('gallery') }}" class="mobile-nav-link {{ request()->routeIs('gallery') ? 'active' : '' }}">
                    Gallery
                </a>
                <a href="{{ route('events') }}" class="mobile-nav-link {{ request()->routeIs('events') ? 'active' : '' }}">
                    Events
                </a>
                <a href="{{ route('contact') }}" class="mobile-nav-link {{ request()->routeIs('contact') ? 'active' : '' }}">
                    Contact
                </a>

                <!-- Admin Login Mobile -->
                @auth
                    <a href="{{ route('admin.dashboard') }}" class="mobile-nav-link bg-amber-600 text-white">
                        <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        Admin Dashboard
                    </a>
                    <form method="POST" action="{{ route('logout') }}" class="px-2">
                        @csrf
                        <button type="submit" class="mobile-nav-link bg-red-600 text-white w-full text-left">
                            <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z"/>
                            </svg>
                            Logout
                        </button>
                    </form>
                @else
                    <a href="{{ route('login') }}" class="mobile-nav-link bg-amber-600 text-white">
                        <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 6h-3V5c0-.55-.45-1-1s-1 .45-1 1v3H8c-.55 0-1 .45-1 1s.45 1 1 1h3v3c0 .55.45 1 1 1s1-.45 1-1v-3h3c.55 0 1-.45 1-1s-.45-1-1-1z"/>
                        </svg>
                        Admin Login
                    </a>
                @endauth
            </div>
        </div>
    </nav>
    @endunless
    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-stone-900 text-amber-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Restaurant Info -->
                <div>
                    <h3 class="text-xl font-bold text-amber-400 mb-4">
                        Kiambu Itaara Grills
                    </h3>
                    <p class="text-amber-100 mb-4 leading-relaxed">
                        Experience the finest nyama choma and grilled foods in Kiambu.
                        Exceptional atmosphere, refreshing drinks, and memorable dining experiences.
                    </p>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-amber-300">Contact Us</h4>
                    <div class="space-y-3 text-amber-100">
                        <div class="flex items-center">
                            <span class="font-medium mr-2">Location:</span>
                            <span>Kiambu, Kenya</span>
                        </div>
                        @php $settings = \App\Models\Setting::first(); @endphp
                        <div class="flex items-center">
                            <span class="font-medium mr-2">Phone:</span>
                            <a href="tel:{{ $settings->contact_phone ?? '+254XXXXXXXXX' }}" class="hover:text-amber-300 transition-colors">
                                {{ $settings->contact_phone ?? '+254 XXX XXX XXX' }}
                            </a>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium mr-2">WhatsApp:</span>
                            <a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', $settings->contact_whatsapp ?? '+254XXXXXXXXX') }}" target="_blank" class="hover:text-amber-300 transition-colors">
                                {{ $settings->contact_whatsapp ?? '+254 XXX XXX XXX' }}
                            </a>
                        </div>
                        @if($settings->contact_email)
                            <div class="flex items-center">
                                <span class="font-medium mr-2">Email:</span>
                                <a href="mailto:{{ $settings->contact_email }}" class="hover:text-amber-300 transition-colors">
                                    {{ $settings->contact_email }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Social Links & Hours -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-amber-300">Follow Us</h4>
                    <div class="flex space-x-6 mb-6">
                        <a href="#" class="text-amber-100 hover:text-amber-400 transition-colors duration-200">
                            Facebook
                        </a>
                        <a href="#" class="text-amber-100 hover:text-amber-400 transition-colors duration-200">
                            Instagram
                        </a>
                        <a href="#" class="text-amber-100 hover:text-amber-400 transition-colors duration-200">
                            TikTok
                        </a>
                    </div>

                    <div>
                        <h5 class="text-sm font-medium text-amber-300 mb-2">Opening Hours</h5>
                        <p class="text-amber-100 text-sm">{{ $settings->opening_hours ?? 'Monday - Sunday: 10:00 AM - 11:00 PM' }}</p>
                    </div>
                </div>
            </div>

            <div class="border-t border-stone-700 mt-8 pt-8 text-center text-amber-200">
                <p>&copy; 2024 Kiambu Itaara Grills. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Loading Screen Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingScreen = document.getElementById('loading-screen');
            const woodenImageUrl = '{{ asset("images/wooden.jpeg") }}';

            // Preload the wooden background image
            const img = new Image();
            img.onload = function() {
                // Image loaded, hide loading screen after a short delay
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');

                    // Initialize fade-in animations
                    setTimeout(() => {
                        const fadeElements = document.querySelectorAll('.fade-in');
                        fadeElements.forEach((element, index) => {
                            setTimeout(() => {
                                element.classList.add('animate-fade-in');
                            }, index * 100);
                        });
                    }, 300);
                }, 500);
            };

            img.onerror = function() {
                // If image fails to load, still hide loading screen
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                }, 1000);
            };

            img.src = woodenImageUrl;

            // Fallback: hide loading screen after 3 seconds regardless
            setTimeout(() => {
                if (!loadingScreen.classList.contains('hidden')) {
                    loadingScreen.classList.add('hidden');
                }
            }, 3000);
        });
    </script>
</body>
</html>
