@extends('layouts.admin')

@section('title', 'Edit Home Page')

@section('content')
<div class="min-h-screen bg-amber-50">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Edit Home Page Content</h1>
            <p class="text-amber-700 mt-2">Update your home page content and see the preview side by side</p>
        </div>

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Edit Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-amber-900 mb-6">Edit Content</h2>
                
                <form action="{{ route('admin.home.update') }}" method="POST" class="space-y-6">
                    @csrf
                    
                    <!-- Hero Section -->
                    <div class="border-b border-amber-200 pb-6">
                        <h3 class="text-lg font-medium text-amber-900 mb-4">Hero Section</h3>
                        
                        <div class="mb-4">
                            <label for="hero_title" class="block text-sm font-medium text-amber-900 mb-2">
                                Hero Title *
                            </label>
                            <input type="text" 
                                   id="hero_title" 
                                   name="hero_title" 
                                   value="{{ old('hero_title', $homeContent->hero_title) }}"
                                   class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('hero_title') border-red-500 @enderror"
                                   required>
                            @error('hero_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="hero_subtitle" class="block text-sm font-medium text-amber-900 mb-2">
                                Hero Subtitle *
                            </label>
                            <textarea id="hero_subtitle" 
                                      name="hero_subtitle" 
                                      rows="3"
                                      class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('hero_subtitle') border-red-500 @enderror"
                                      required>{{ old('hero_subtitle', $homeContent->hero_subtitle) }}</textarea>
                            @error('hero_subtitle')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- About Section -->
                    <div class="border-b border-amber-200 pb-6">
                        <h3 class="text-lg font-medium text-amber-900 mb-4">About Section</h3>
                        
                        <div class="mb-4">
                            <label for="about_title" class="block text-sm font-medium text-amber-900 mb-2">
                                About Title *
                            </label>
                            <input type="text" 
                                   id="about_title" 
                                   name="about_title" 
                                   value="{{ old('about_title', $homeContent->about_title) }}"
                                   class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('about_title') border-red-500 @enderror"
                                   required>
                            @error('about_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="about_description" class="block text-sm font-medium text-amber-900 mb-2">
                                About Description *
                            </label>
                            <textarea id="about_description" 
                                      name="about_description" 
                                      rows="4"
                                      class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('about_description') border-red-500 @enderror"
                                      required>{{ old('about_description', $homeContent->about_description) }}</textarea>
                            @error('about_description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Features Section -->
                    <div class="pb-6">
                        <h3 class="text-lg font-medium text-amber-900 mb-4">Features Section</h3>
                        
                        <!-- Feature 1 -->
                        <div class="mb-6 p-4 bg-amber-50 rounded-lg">
                            <h4 class="font-medium text-amber-900 mb-3">Feature 1</h4>
                            <div class="mb-3">
                                <label for="feature_1_title" class="block text-sm font-medium text-amber-900 mb-1">
                                    Title *
                                </label>
                                <input type="text" 
                                       id="feature_1_title" 
                                       name="feature_1_title" 
                                       value="{{ old('feature_1_title', $homeContent->feature_1_title) }}"
                                       class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('feature_1_title') border-red-500 @enderror"
                                       required>
                                @error('feature_1_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="feature_1_description" class="block text-sm font-medium text-amber-900 mb-1">
                                    Description *
                                </label>
                                <textarea id="feature_1_description" 
                                          name="feature_1_description" 
                                          rows="2"
                                          class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('feature_1_description') border-red-500 @enderror"
                                          required>{{ old('feature_1_description', $homeContent->feature_1_description) }}</textarea>
                                @error('feature_1_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Feature 2 -->
                        <div class="mb-6 p-4 bg-amber-50 rounded-lg">
                            <h4 class="font-medium text-amber-900 mb-3">Feature 2</h4>
                            <div class="mb-3">
                                <label for="feature_2_title" class="block text-sm font-medium text-amber-900 mb-1">
                                    Title *
                                </label>
                                <input type="text" 
                                       id="feature_2_title" 
                                       name="feature_2_title" 
                                       value="{{ old('feature_2_title', $homeContent->feature_2_title) }}"
                                       class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('feature_2_title') border-red-500 @enderror"
                                       required>
                                @error('feature_2_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="feature_2_description" class="block text-sm font-medium text-amber-900 mb-1">
                                    Description *
                                </label>
                                <textarea id="feature_2_description" 
                                          name="feature_2_description" 
                                          rows="2"
                                          class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('feature_2_description') border-red-500 @enderror"
                                          required>{{ old('feature_2_description', $homeContent->feature_2_description) }}</textarea>
                                @error('feature_2_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Feature 3 -->
                        <div class="mb-6 p-4 bg-amber-50 rounded-lg">
                            <h4 class="font-medium text-amber-900 mb-3">Feature 3</h4>
                            <div class="mb-3">
                                <label for="feature_3_title" class="block text-sm font-medium text-amber-900 mb-1">
                                    Title *
                                </label>
                                <input type="text" 
                                       id="feature_3_title" 
                                       name="feature_3_title" 
                                       value="{{ old('feature_3_title', $homeContent->feature_3_title) }}"
                                       class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('feature_3_title') border-red-500 @enderror"
                                       required>
                                @error('feature_3_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="feature_3_description" class="block text-sm font-medium text-amber-900 mb-1">
                                    Description *
                                </label>
                                <textarea id="feature_3_description" 
                                          name="feature_3_description" 
                                          rows="2"
                                          class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('feature_3_description') border-red-500 @enderror"
                                          required>{{ old('feature_3_description', $homeContent->feature_3_description) }}</textarea>
                                @error('feature_3_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-between items-center pt-6 border-t border-amber-200">
                        <a href="{{ route('admin.dashboard') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                            ← Back to Dashboard
                        </a>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md hover:bg-amber-700 transition-colors font-medium">
                            Update Home Page
                        </button>
                    </div>
                </form>
            </div>

            <!-- Live Preview -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-amber-600 text-white p-4">
                    <h2 class="text-xl font-bold">Live Preview</h2>
                    <p class="text-amber-100 text-sm">See how your changes will look</p>
                </div>
                
                <div class="h-96 overflow-y-auto">
                    <iframe src="{{ route('home') }}" class="w-full h-full border-0" style="min-height: 800px;"></iframe>
                </div>
                
                <div class="p-4 bg-amber-50 border-t">
                    <a href="{{ route('home') }}" target="_blank" class="text-amber-600 hover:text-amber-800 font-medium text-sm">
                        Open full page in new tab →
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
