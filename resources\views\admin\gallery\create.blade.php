@extends('layouts.app')

@section('title', 'Upload Photo - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Upload New Photo</h1>
            <p class="text-amber-700 mt-2">Add a beautiful photo to your gallery</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form method="POST" action="{{ route('admin.gallery.store') }}" enctype="multipart/form-data">
                @csrf

                <!-- Photo Upload -->
                <div class="mb-6">
                    <label for="image" class="block text-sm font-medium text-amber-900 mb-2">
                        Photo *
                    </label>
                    <input type="file" 
                           id="image" 
                           name="image" 
                           accept="image/*"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('image') border-red-500 @enderror"
                           required>
                    @error('image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-amber-600">Upload a high-quality photo (JPG, PNG, max 2MB)</p>
                </div>

                <!-- Photo Title -->
                <div class="mb-6">
                    <label for="title" class="block text-sm font-medium text-amber-900 mb-2">
                        Photo Title *
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title') }}"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('title') border-red-500 @enderror"
                           placeholder="e.g., Delicious Nyama Choma, Restaurant Interior"
                           required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div class="mb-6">
                    <label for="category_id" class="block text-sm font-medium text-amber-900 mb-2">
                        Category *
                    </label>
                    <select id="category_id" 
                            name="category_id" 
                            class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('category_id') border-red-500 @enderror"
                            required>
                        <option value="">Select a category</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @if($categories->count() == 0)
                        <p class="mt-1 text-sm text-amber-600">
                            <a href="{{ route('admin.categories.create') }}" class="text-amber-700 hover:text-amber-900 underline">Create a category first</a>
                        </p>
                    @endif
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-amber-900 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3"
                              class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('description') border-red-500 @enderror"
                              placeholder="Optional description or caption for the photo...">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('admin.gallery.index') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        ← Back to Gallery
                    </a>
                    <div class="space-x-3">
                        <button type="button" onclick="window.history.back()" class="px-4 py-2 border border-amber-300 text-amber-700 rounded-md hover:bg-amber-50 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-amber-700 transition-colors">
                            Upload Photo
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Tips -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-900 mb-2">Photo Tips:</h3>
            <ul class="text-sm text-blue-800 space-y-1">
                <li>• Use high-quality, well-lit photos</li>
                <li>• Food photos should be appetizing and colorful</li>
                <li>• Restaurant photos should show the atmosphere</li>
                <li>• Event photos should capture the energy and fun</li>
            </ul>
        </div>
    </div>
</div>
@endsection
