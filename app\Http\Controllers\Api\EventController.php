<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\JsonResponse;

class EventController extends Controller
{
    /**
     * Get all upcoming events.
     */
    public function index(): JsonResponse
    {
        $events = Event::upcoming()->orderByDate()->get();

        return response()->json([
            'success' => true,
            'data' => $events
        ]);
    }

    /**
     * Get all events (including past events).
     */
    public function all(): JsonResponse
    {
        $events = Event::orderBy('event_date', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $events
        ]);
    }
}
