@extends('layouts.app')

@section('title', 'Edit Menu Item - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-amber-900">Edit Menu Item</h1>
            <p class="text-amber-700 mt-2">Update the menu item information</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form method="POST" action="{{ route('admin.menu-items.update', $menuItem) }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <!-- Current Image -->
                @if($menuItem->image_url)
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-amber-900 mb-2">Current Image</label>
                        <img src="{{ asset('storage/' . $menuItem->image_url) }}" alt="{{ $menuItem->name }}" class="w-32 h-32 object-cover rounded-lg">
                    </div>
                @endif

                <!-- Item Name -->
                <div class="mb-6">
                    <label for="name" class="block text-sm font-medium text-amber-900 mb-2">
                        Item Name *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $menuItem->name) }}"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('name') border-red-500 @enderror"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div class="mb-6">
                    <label for="category_id" class="block text-sm font-medium text-amber-900 mb-2">
                        Category *
                    </label>
                    <select id="category_id" 
                            name="category_id" 
                            class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('category_id') border-red-500 @enderror"
                            required>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ old('category_id', $menuItem->category_id) == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Price -->
                <div class="mb-6">
                    <label for="price" class="block text-sm font-medium text-amber-900 mb-2">
                        Price (KSh) *
                    </label>
                    <input type="number" 
                           id="price" 
                           name="price" 
                           value="{{ old('price', $menuItem->price) }}"
                           step="0.01"
                           min="0"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('price') border-red-500 @enderror"
                           required>
                    @error('price')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-amber-900 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3"
                              class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('description') border-red-500 @enderror">{{ old('description', $menuItem->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Image Upload -->
                <div class="mb-6">
                    <label for="image" class="block text-sm font-medium text-amber-900 mb-2">
                        Update Photo
                    </label>
                    <input type="file" 
                           id="image" 
                           name="image" 
                           accept="image/*"
                           class="w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 @error('image') border-red-500 @enderror">
                    @error('image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-amber-600">Leave empty to keep current image</p>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between items-center">
                    <a href="{{ route('admin.menu-items.index') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        ← Back to Menu Items
                    </a>
                    <div class="space-x-3">
                        <button type="button" onclick="window.history.back()" class="px-4 py-2 border border-amber-300 text-amber-700 rounded-md hover:bg-amber-50 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="bg-amber-600 text-white px-6 py-2 rounded-md font-semibold hover:bg-amber-700 transition-colors">
                            Update Menu Item
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Delete Section -->
        <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-red-900 mb-2">Delete Menu Item</h3>
            <p class="text-sm text-red-700 mb-3">This will permanently remove this item from your menu.</p>
            <form method="POST" action="{{ route('admin.menu-items.destroy', $menuItem) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this menu item? This action cannot be undone.')">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors">
                    Delete Menu Item
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
