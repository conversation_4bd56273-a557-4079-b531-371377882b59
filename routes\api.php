<?php

use App\Http\Controllers\Api\MenuController;
use App\Http\Controllers\Api\GalleryController;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\SettingController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API routes for the React frontend
Route::prefix('v1')->group(function () {
    // Menu routes
    Route::get('/menu', [MenuController::class, 'index']);
    Route::get('/menu/category/{categoryId}', [MenuController::class, 'byCategory']);
    
    // Gallery routes
    Route::get('/gallery', [GalleryController::class, 'index']);
    Route::get('/gallery/category/{categoryId}', [GalleryController::class, 'byCategory']);
    
    // Event routes
    Route::get('/events', [EventController::class, 'index']);
    Route::get('/events/all', [EventController::class, 'all']);
    
    // Settings routes
    Route::get('/settings', [SettingController::class, 'index']);
});
