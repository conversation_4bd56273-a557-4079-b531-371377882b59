@extends('layouts.app')

@section('title', 'Gallery - Kiam<PERSON>aara Grills')
@section('description', 'Take a look at our vibrant atmosphere, delicious food, and memorable events at Kiambu Itaara Grills.')

@section('content')
<div class="min-h-screen bg-amber-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 fade-in">
            <h1 class="text-4xl md:text-5xl font-bold text-amber-900 mb-4">Gallery</h1>
            <p class="text-xl text-amber-700 max-w-2xl mx-auto">
                Take a look at our vibrant atmosphere, delicious food, and memorable events
            </p>
        </div>

        @if($categories->count() > 0)
            @foreach($categories as $category)
                <div class="mb-16 fade-in">
                    <h2 class="text-3xl font-bold text-amber-900 mb-8 text-center">{{ $category->name }}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach($category->galleryItems as $item)
                            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                                <div class="relative overflow-hidden">
                                    <img src="{{ Storage::url($item->image_url) }}" alt="{{ $item->title }}" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM8.5 16L12 13.5 15.5 16 12 18.5 8.5 16z"/>
                                        </svg>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <h3 class="text-lg font-semibold text-amber-900 mb-2">{{ $item->title }}</h3>
                                    @if($item->description)
                                        <p class="text-amber-700 text-sm">{{ $item->description }}</p>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        @else
            <div class="text-center py-20">
                <div class="bg-white rounded-lg shadow-lg p-12 max-w-2xl mx-auto fade-in">
                    <svg class="w-16 h-16 text-amber-600 mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9,2V7.38L10.5,8.88L15.12,4.26L19.74,8.88L12,16.62L4.26,8.88L8.88,4.26L9,4.15V2M12,5L7,10L12,15L17,10L12,5Z"/>
                    </svg>
                    <h3 class="text-2xl font-bold text-amber-900 mb-4">Gallery Coming Soon</h3>
                    <p class="text-amber-700 text-lg font-medium">We're preparing beautiful photos to showcase our restaurant</p>
                    <p class="text-amber-600 mt-2">Check back soon for amazing visuals of our food and atmosphere</p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
