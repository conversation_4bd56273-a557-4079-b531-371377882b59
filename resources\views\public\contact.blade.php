@extends('layouts.app')

@section('title', 'Contact Us - <PERSON><PERSON><PERSON>')
@section('description', 'Get in touch with us for reservations, events, or any inquiries. We\'re here to make your experience exceptional.')

@section('content')
<div class="min-h-screen bg-amber-50 py-12 smoke-pattern">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 fade-in">
            <h1 class="text-4xl md:text-5xl font-bold text-amber-900 mb-4">Contact Us</h1>
            <p class="text-xl text-amber-700 max-w-2xl mx-auto">
                Get in touch with us for reservations, events, or any inquiries. 
                We're here to make your experience exceptional.
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow-lg p-8 fade-in">
                <h2 class="text-2xl font-bold text-amber-900 mb-6">Get in Touch</h2>
                
                <div class="space-y-6">
                    <div class="flex items-start">
                        <div class="bg-amber-600 p-3 rounded-full mr-4 flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-amber-900">Location</h3>
                            <p class="text-amber-700">Kiambu, Kenya</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-amber-600 p-3 rounded-full mr-4 flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-amber-900">Phone</h3>
                            <p class="text-amber-700">{{ $settings->contact_phone ?? '+254 XXX XXX XXX' }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-amber-600 p-3 rounded-full mr-4 flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-amber-900">WhatsApp</h3>
                            <a href="https://wa.me/{{ str_replace(['+', ' '], '', $settings->contact_whatsapp ?? '+254XXXXXXXXX') }}" class="text-amber-700 hover:text-amber-900 transition-colors">
                                {{ $settings->contact_whatsapp ?? '+254 XXX XXX XXX' }}
                            </a>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-amber-600 p-3 rounded-full mr-4 flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-amber-900">Email</h3>
                            <a href="mailto:{{ $settings->contact_email ?? '<EMAIL>' }}" class="text-amber-700 hover:text-amber-900 transition-colors">
                                {{ $settings->contact_email ?? '<EMAIL>' }}
                            </a>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="bg-amber-600 p-3 rounded-full mr-4 flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM8.5 16L12 13.5 15.5 16 12 18.5 8.5 16z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-amber-900">Opening Hours</h3>
                            <p class="text-amber-700">{{ $settings->opening_hours ?? 'Monday - Sunday: 10:00 AM - 11:00 PM' }}</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8 pt-6 border-t border-amber-200">
                    <h3 class="font-semibold mb-4 text-amber-900">Follow Us</h3>
                    <div class="flex space-x-6">
                        @if($settings->facebook_url)
                            <a href="{{ $settings->facebook_url }}" target="_blank" class="text-amber-600 hover:text-amber-800 font-medium transition-colors">Facebook</a>
                        @endif
                        @if($settings->instagram_url)
                            <a href="{{ $settings->instagram_url }}" target="_blank" class="text-amber-600 hover:text-amber-800 font-medium transition-colors">Instagram</a>
                        @endif
                        @if($settings->tiktok_url)
                            <a href="{{ $settings->tiktok_url }}" target="_blank" class="text-amber-600 hover:text-amber-800 font-medium transition-colors">TikTok</a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Map Placeholder -->
            <div class="bg-white rounded-lg shadow-lg p-8 fade-in">
                <h2 class="text-2xl font-bold text-amber-900 mb-6">Find Us</h2>
                <div class="rounded-lg overflow-hidden shadow-lg">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.924646594641!2d36.83284287496555!3d-1.2127147987757112!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f3db33040b171%3A0xfe4bb7f148f6de98!2sItara%20Grill!5e0!3m2!1sen!2ske!4v1755690184168!5m2!1sen!2ske"
                            width="100%"
                            height="300"
                            style="border:0;"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
                <div class="mt-4 text-center">
                    <a href="https://maps.google.com/?q=Itara+Grill,+Kiambu"
                       target="_blank"
                       class="inline-flex items-center text-amber-600 hover:text-amber-800 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12,2C8.13,2 5,5.13 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9C19,5.13 15.87,2 12,2M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z"/>
                        </svg>
                        Open in Google Maps
                    </a>
                </div>

                <!-- Quick Contact Actions -->
                <div class="mt-6 grid grid-cols-2 gap-4">
                    <a href="tel:{{ $settings->contact_phone ?? '+254XXXXXXXXX' }}" class="bg-amber-600 text-white px-4 py-3 rounded-lg text-center font-semibold hover:bg-amber-700 transition-colors">
                        Call Now
                    </a>
                    <a href="https://wa.me/{{ str_replace(['+', ' '], '', $settings->contact_whatsapp ?? '+254XXXXXXXXX') }}" target="_blank" class="bg-green-600 text-white px-4 py-3 rounded-lg text-center font-semibold hover:bg-green-700 transition-colors">
                        WhatsApp
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
