@extends('layouts.app')

@section('title', 'Manage Gallery - Admin')

@section('content')
<div class="min-h-screen bg-amber-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-amber-900">Manage Gallery</h1>
                <p class="text-amber-700 mt-2">Upload and organize photos of your restaurant, food, and events</p>
            </div>
            <a href="{{ route('admin.gallery.create') }}" class="bg-amber-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors">
                Upload Photos
            </a>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <!-- Gallery Grid -->
        @if($galleryItems->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($galleryItems as $item)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <!-- Image -->
                        <div class="aspect-square">
                            <img src="{{ asset('storage/' . $item->image_url) }}" alt="{{ $item->title }}" class="w-full h-full object-cover">
                        </div>

                        <!-- Content -->
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-amber-900 mb-1">{{ $item->title }}</h3>
                            <p class="text-sm text-amber-600 mb-2">{{ $item->category->name }}</p>

                            @if($item->description)
                                <p class="text-amber-700 text-sm mb-3">{{ Str::limit($item->description, 80) }}</p>
                            @endif

                            <!-- Actions -->
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-amber-600">{{ $item->created_at->format('M d, Y') }}</span>
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.gallery.edit', $item) }}" class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700 transition-colors">
                                        Edit
                                    </a>
                                    <form method="POST" action="{{ route('admin.gallery.destroy', $item) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this photo?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700 transition-colors">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div class="bg-white rounded-lg shadow-lg p-12 text-center">
                <svg class="mx-auto h-16 w-16 text-amber-600 mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9,2V7.38L10.5,8.88L15.12,4.26L19.74,8.88L12,16.62L4.26,8.88L8.88,4.26L9,4.15V2M12,5L7,10L12,15L17,10L12,5Z"/>
                </svg>
                <h3 class="text-xl font-medium text-amber-900 mb-2">No photos yet</h3>
                <p class="text-amber-700 mb-6">Start building your gallery by uploading your first photo</p>
                <a href="{{ route('admin.gallery.create') }}" class="bg-amber-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-700 transition-colors">
                    Upload First Photo
                </a>
            </div>
        @endif

        <!-- Back to Dashboard -->
        <div class="mt-8">
            <a href="{{ route('admin.dashboard') }}" class="text-amber-600 hover:text-amber-800 font-medium">
                ← Back to Dashboard
            </a>
        </div>
    </div>
</div>
@endsection
