@props(['value', 'field', 'model' => 'settings', 'id' => null, 'placeholder' => '', 'type' => 'text', 'class' => ''])

<div class="inline-edit-container relative group" data-field="{{ $field }}" data-model="{{ $model }}" data-id="{{ $id }}">
    @auth
        <!-- Edit <PERSON> (Admin Only) -->
        <button class="edit-btn absolute -top-2 -right-2 bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-10 text-xs hover:bg-blue-700" 
                onclick="startEdit(this)">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
            </svg>
        </button>
    @endauth
    
    <!-- Display Content -->
    <div class="display-content {{ $class }}">
        {{ $value ?: $placeholder }}
    </div>
    
    <!-- Edit Form (Hidden by default) -->
    <div class="edit-form hidden">
        @if($type === 'textarea')
            <textarea class="edit-input w-full p-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 {{ $class }}" 
                      placeholder="{{ $placeholder }}">{{ $value }}</textarea>
        @else
            <input type="{{ $type }}" 
                   class="edit-input w-full p-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 {{ $class }}" 
                   value="{{ $value }}" 
                   placeholder="{{ $placeholder }}">
        @endif
        
        <div class="flex gap-2 mt-2">
            <button onclick="saveEdit(this)" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                Save
            </button>
            <button onclick="cancelEdit(this)" class="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700">
                Cancel
            </button>
        </div>
    </div>
</div>
