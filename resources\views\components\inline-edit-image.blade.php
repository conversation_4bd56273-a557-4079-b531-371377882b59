@props(['src', 'alt', 'field', 'model' => 'menu_item', 'id', 'class' => 'w-full h-48 object-cover'])

<div class="inline-edit-image-container relative group" data-field="{{ $field }}" data-model="{{ $model }}" data-id="{{ $id }}">
    @auth
        <!-- Edit <PERSON> (Admin Only) -->
        <button class="edit-image-btn absolute top-2 right-2 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-10 hover:bg-blue-700" 
                onclick="startImageEdit(this)">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9,2V7.38L10.5,8.88L15.12,4.26L19.74,8.88L12,16.62L4.26,8.88L8.88,4.26L9,4.15V2M12,5L7,10L12,15L17,10L12,5Z"/>
            </svg>
        </button>
    @endauth
    
    <!-- Display Image -->
    <div class="image-display">
        @if($src)
            <img src="{{ asset('storage/' . $src) }}" alt="{{ $alt }}" class="{{ $class }}">
        @else
            <div class="{{ $class }} bg-gray-100 flex items-center justify-center">
                <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9,2V7.38L10.5,8.88L15.12,4.26L19.74,8.88L12,16.62L4.26,8.88L8.88,4.26L9,4.15V2M12,5L7,10L12,15L17,10L12,5Z"/>
                </svg>
            </div>
        @endif
    </div>
    
    <!-- Edit Form (Hidden by default) -->
    <div class="edit-image-form hidden absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg p-4 max-w-sm w-full">
            <h3 class="text-lg font-semibold mb-3">Change Image</h3>
            
            <!-- Upload New -->
            <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Upload New Image</label>
                <input type="file" class="image-upload w-full p-2 border border-gray-300 rounded" accept="image/*">
            </div>
            
            <!-- Or Choose from Gallery -->
            <div class="mb-4">
                <label class="block text-sm font-medium mb-1">Or Choose from Gallery</label>
                <select class="gallery-select w-full p-2 border border-gray-300 rounded">
                    <option value="">Select from gallery...</option>
                    <!-- Will be populated via AJAX -->
                </select>
            </div>
            
            <div class="flex gap-2">
                <button onclick="saveImageEdit(this)" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                    Save
                </button>
                <button onclick="cancelImageEdit(this)" class="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
