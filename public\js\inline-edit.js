// Inline Editing JavaScript
window.inlineEdit = {
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
    
    // Text Editing Functions
    startEdit: function(button) {
        const container = button.closest('.inline-edit-container');
        const displayContent = container.querySelector('.display-content');
        const editForm = container.querySelector('.edit-form');
        const input = editForm.querySelector('.edit-input');
        
        // Store original value
        container.dataset.originalValue = input.value;
        
        // Hide display, show edit form
        displayContent.style.display = 'none';
        editForm.classList.remove('hidden');
        
        // Focus input
        input.focus();
        if (input.type === 'text') {
            input.select();
        }
    },
    
    saveEdit: function(button) {
        const container = button.closest('.inline-edit-container');
        const input = container.querySelector('.edit-input');
        const field = container.dataset.field;
        const model = container.dataset.model;
        const id = container.dataset.id;
        
        // Show loading
        button.textContent = 'Saving...';
        button.disabled = true;
        
        // Prepare data
        const data = {
            _token: this.csrfToken,
            field: field,
            value: input.value
        };
        
        // Determine URL based on model
        let url;
        if (model === 'settings') {
            url = '/admin/settings/update-field';
        } else if (model === 'menu_item') {
            url = `/admin/menu-items/${id}/update-field`;
        } else if (model === 'event') {
            url = `/admin/events/${id}/update-field`;
        }
        
        // Send AJAX request
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update display content
                const displayContent = container.querySelector('.display-content');
                displayContent.textContent = input.value || container.dataset.placeholder || '';
                
                // Hide edit form, show display
                this.cancelEdit(button);
                
                // Show success message
                this.showMessage('Updated successfully!', 'success');
            } else {
                throw new Error(data.message || 'Update failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showMessage('Update failed: ' + error.message, 'error');
        })
        .finally(() => {
            button.textContent = 'Save';
            button.disabled = false;
        });
    },
    
    cancelEdit: function(button) {
        const container = button.closest('.inline-edit-container');
        const displayContent = container.querySelector('.display-content');
        const editForm = container.querySelector('.edit-form');
        const input = editForm.querySelector('.edit-input');
        
        // Restore original value
        if (container.dataset.originalValue !== undefined) {
            input.value = container.dataset.originalValue;
        }
        
        // Show display, hide edit form
        displayContent.style.display = '';
        editForm.classList.add('hidden');
    },
    
    // Image Editing Functions
    startImageEdit: function(button) {
        const container = button.closest('.inline-edit-image-container');
        const editForm = container.querySelector('.edit-image-form');
        const gallerySelect = editForm.querySelector('.gallery-select');
        
        // Load gallery options
        this.loadGalleryOptions(gallerySelect);
        
        // Show edit form
        editForm.classList.remove('hidden');
    },
    
    saveImageEdit: function(button) {
        const container = button.closest('.inline-edit-image-container');
        const fileInput = container.querySelector('.image-upload');
        const gallerySelect = container.querySelector('.gallery-select');
        const field = container.dataset.field;
        const model = container.dataset.model;
        const id = container.dataset.id;
        
        // Show loading
        button.textContent = 'Saving...';
        button.disabled = true;
        
        // Prepare form data
        const formData = new FormData();
        formData.append('_token', this.csrfToken);
        formData.append('field', field);
        
        if (fileInput.files[0]) {
            formData.append('image', fileInput.files[0]);
        } else if (gallerySelect.value) {
            formData.append('gallery_image_id', gallerySelect.value);
        } else {
            this.showMessage('Please select an image or upload a new one', 'error');
            button.textContent = 'Save';
            button.disabled = false;
            return;
        }
        
        // Determine URL
        let url;
        if (model === 'menu_item') {
            url = `/admin/menu-items/${id}/update-image`;
        } else if (model === 'event') {
            url = `/admin/events/${id}/update-image`;
        }
        
        // Send AJAX request
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update image display
                const img = container.querySelector('.image-display img');
                if (img) {
                    img.src = data.image_url;
                } else {
                    // Create new img element if none exists
                    const imageDisplay = container.querySelector('.image-display');
                    imageDisplay.innerHTML = `<img src="${data.image_url}" alt="Updated image" class="${img?.className || 'w-full h-48 object-cover'}">`;
                }
                
                // Hide edit form
                this.cancelImageEdit(button);
                
                // Show success message
                this.showMessage('Image updated successfully!', 'success');
            } else {
                throw new Error(data.message || 'Update failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showMessage('Update failed: ' + error.message, 'error');
        })
        .finally(() => {
            button.textContent = 'Save';
            button.disabled = false;
        });
    },
    
    cancelImageEdit: function(button) {
        const container = button.closest('.inline-edit-image-container');
        const editForm = container.querySelector('.edit-image-form');
        const fileInput = container.querySelector('.image-upload');
        const gallerySelect = container.querySelector('.gallery-select');
        
        // Clear inputs
        fileInput.value = '';
        gallerySelect.value = '';
        
        // Hide edit form
        editForm.classList.add('hidden');
    },
    
    loadGalleryOptions: function(select) {
        fetch('/admin/gallery/options')
            .then(response => response.json())
            .then(data => {
                select.innerHTML = '<option value="">Select from gallery...</option>';
                data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.title} (${item.category})`;
                    select.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading gallery options:', error);
            });
    },
    
    showMessage: function(message, type) {
        // Create message element
        const messageEl = document.createElement('div');
        messageEl.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
        messageEl.textContent = message;
        
        // Add to page
        document.body.appendChild(messageEl);
        
        // Remove after 3 seconds
        setTimeout(() => {
            messageEl.remove();
        }, 3000);
    }
};

    deleteMenuItem: function(id) {
        if (!confirm('Are you sure you want to delete this menu item?')) {
            return;
        }

        fetch(`/admin/menu-items/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the menu item card from the page
                const menuCard = document.querySelector(`[data-menu-item-id="${id}"]`);
                if (menuCard) {
                    menuCard.remove();
                }
                this.showMessage('Menu item deleted successfully!', 'success');

                // Reload page to update layout
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                throw new Error(data.message || 'Delete failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showMessage('Delete failed: ' + error.message, 'error');
        });
    }
};

// Global functions for onclick handlers
function startEdit(button) { window.inlineEdit.startEdit(button); }
function saveEdit(button) { window.inlineEdit.saveEdit(button); }
function cancelEdit(button) { window.inlineEdit.cancelEdit(button); }
function startImageEdit(button) { window.inlineEdit.startImageEdit(button); }
function saveImageEdit(button) { window.inlineEdit.saveImageEdit(button); }
function cancelImageEdit(button) { window.inlineEdit.cancelImageEdit(button); }
function deleteMenuItem(id) { window.inlineEdit.deleteMenuItem(id); }
